import os
import json
from resolve_composites import resolve_composites
from resolve_references import resolve_references

def main():
    open_api_file_path = "latest_api/api_spec.json"
    open_api_spec = json.load(open(open_api_file_path, "r"))

    open_api_spec = resolve_composites(open_api_spec)
    open_api_spec = resolve_references(open_api_spec)

    open_api_save_path = "fixed_APISpecs/api_spec.json"
    os.makedirs(os.path.dirname(open_api_save_path), exist_ok=True)
    json.dump(open_api_spec, open(open_api_save_path, "w"), indent=2)

if __name__ == "__main__":
    main()