{"openapi": "3.1.0", "info": {"title": "Computer Telephony Integration API v1", "description": "The Computer Telephony Integration (CTI) API provides persistence of customer call data between automated technologies and live agents \nas defined by data contracts.\n\n**Key Business Values**\n- contact center calls are more effecient and close faster with no repeating of information\n- improves the customer experience in transitioning between the automated solution and a live agent", "version": "1.0.0"}, "servers": [{"url": "https://onlinetools.ups.com/api/customer-service/cti/{version}", "description": "URL for OAuth based authentication that can be used for any environment (production, staging, product test) via hosts file.", "variables": {"version": {"default": "v1", "enum": ["v1"]}}}], "tags": [{"name": "Live Agent", "description": "Endpoints utilized by support agents"}, {"name": "Automated Tools", "description": "Endpoints utilized by automated support tools"}], "paths": {"/utility/data": {"post": {"summary": "Gets the data from CTI.", "description": "Retrieves data stored in CTI DB to a (transferred) agent and updates CTI DB with that agents details.", "operationId": "ctidata", "tags": ["Live Agent"], "security": [{"OAuth2": []}], "parameters": [{"$ref": "#/components/parameters/transId"}, {"$ref": "#/components/parameters/transactionSrc"}], "requestBody": {"description": "Gets the data from CTI DB and Posts to CEC/Tenfold", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CtiDataRequestV1"}, "example": {"sCTICallID": "169fa059********1125146754", "sRegionCode": "NA", "sLanguageCode": "En", "sRequestTimestamp": "10/14/2021 13:36", "sAgentID": "1012052", "sAgentName": "1012052", "sAgentBuildingMnemonic": "Tenfold", "sAgentExtension": "1011002", "sAgentWorkstationName": "gbstustcc", "sCallTransferIndicator": "0", "sTransferAgentID": "1012011", "sTransferAgentName": "GD000033333", "sTransferAgentBuildingMnemonic": "NJRAR", "sTransferAgentExtension": "1012001", "sTransferAgentWorkstationName": "WKSP11111111", "sTransferDateTime": "2020-08-21 11:34:38.757"}}}, "required": true}, "responses": {"200": {"description": "Successful operation", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CtiDataResponseV1"}, "example": {"ctiCallId": "08d6193e041020251030540382", "uuid": "169fa059-d44d-1578-899d-aa6889b55c11", "region": "NA", "language": "EN", "app": "AT", "appDesc": "Domestic Tracking", "ani": "********", "iata": "US", "iataTo": "US", "invoiceDateIdr": "Y", "billAuthStatus": "04", "billAuthStatusDesc": "Description of the bill ", "accountStatus": "S", "accountStatusDesc": "Account Stat Description", "callerRole": "ND", "callerRoleDesc": "Not Determined", "callerCategory": "000", "callerCategoryDesc": "Not Categorized", "callerCategoryGrp": "SH", "callerCategoryGrpDesc": "Shipper", "callerIntent": "track_package", "ucid": "00FA080001007A6813BF04", "ivrDate": "3312025", "ivrTime": "**********", "version": "0009", "transferInitiator": "S", "transferInitiatorDesc": "System Initiated", "reasonCode": "00001", "reasonCodeDesc": "00006", "agentOpenTooltip": "Tooltip for an agent when popup is shown", "paymentOption": "1", "paymentOptionDesc": "Description is text", "trackingNumber": "1Z4175YY0394004855", "trackingNumber1": "1Z0AE288DA43450054", "trackingNumber2": "1Z0AE288DA43450053", "trackingNumber3": "1Z0AE288DA43450052", "shipperAccountNumber": "re5845", "phoneExtension": "", "gsrEligibility": "Y", "enrollmentStatus": "Y", "creditCardEligibility": "Y", "myChoiceTokenization": "1", "confirmationNumber": "", "overSizeIdr": "Y", "ivrServiceCode": "09", "ivrServiceCodeDesc": "International", "pickupDate": "", "shippingLabelsIdr": "", "accountLocation": "", "shipperAuthIdr": "", "callTime": "", "customerCloseTime": "", "ivrCloseTime": "", "readyTime": "", "commitTime": "", "totalQuantity": "", "destinationCount": "", "newZipCode": "", "newCity": "", "newState": "", "newIATA": "", "newStreetNumber": "", "newStreetName": "", "newSecondaryInfo": "", "customerProfileAddressId": "", "customerProfileContactId": "", "customerProfileShipperIATA": "", "accountIATA": "", "customerProfileIVRCustomerId": "", "originZipCode": "", "destZipCode": "", "residentialAddressIdr": "", "packageLocationStatus": "OS", "packageLocationStatusDesc": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "repeatCallCount": "1", "agentTransferMsgs": [""], "transferMsgTrackingNumber": "1Z0AE288DA43450023", "lastTransferMsg": "", "weight": "", "length": "", "width": "", "height": "", "geoCodeLatitude": "", "geoCodeLongitude": "", "internationalIdr": "", "internationaIdrDesc": "", "altPhone": "1Z", "trackingNumberType": "", "trackingNumberTypeDesc": "", "dcrAction": "", "dcrActionDesc": "", "accessPointZipCode": "91331", "packageActivityType": "PACKAGE HELD - PROCESSING DELAY", "packageActivityTypeDesc": "", "packageActivityStatus": "OS", "packageActivityStatusDesc": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "nextExpEventDate": "********", "nextExpEventTime": "112515", "amazonIdr": "", "amazonIdrDesc": "Not Amazon Call", "companyName": "", "contactName": "", "infoNoticeNumber": "", "futureDate": "", "returnStatusCode": "", "returnStatusCodeDesc": "", "invoiceAmount": "", "invoiceDate": "", "cserEventId": "CSER********1125150949768", "vaConversationId": "169fa059-d44d-1578-899d-aa6889b55c11", "heavyGoodsIdr": "", "vaTranscript": "text up to 32000 chars", "lastWebPageVisited": "", "vaIntent": "channelingstrategy_phone_domestictracking", "vaIntentDesc": "Domestic Tracking", "firstName": "VA_FirstName", "lastName": "VA_LastName", "vaConversationDuration": "0", "isVADataIdr": "1", "callHistories": [{"seqNo": "1'", "ani": "0", "ctiCallId": "169fa059032820251641165569", "trackingNumber": "1Z4175YY0394004855", "packageActivityStatus": "OS", "packageActivityStatusDesc": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "callTime": "2025-03-28 16", "repeatCallCount": "1", "iata": "US", "iataTo": "US", "language": "EN", "packageLocationStatus": "1", "packageLocationStatusDesc": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "destPostalCode": "", "nextExpEventDate": "2025-03-28T00:00:00.000Z", "nextExpEventTime": "16-41-17", "myChoicePkgIdr": "0", "isVADataIdr": "1", "cserEventId": "CSER202503281641170653338"}]}}}}, "400": {"description": "Bad Request", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "Malformed JSON request."}, {"code": "TBD", "message": "Missing transId."}]}}}}}, "401": {"description": "Unauthorized", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "250002", "message": "Invalid Authentication Information."}]}}}}}, "404": {"description": "Resource Not Found", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "The requested <TBD> was not found."}]}}}}}, "500": {"description": "Server Error", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "Internal Server Error."}]}}}}}}}}, "/digital": {"post": {"summary": "Store and update digital data.", "description": "Creates and updates database records upon customer selection of 'Click To Call' in the Chatbot Virtual Assistant.\n  - Creates a record in CTI DB for intent and tracking data.\n  - Creates a record in CSERS (Customer Service Events Repository & Services) for profile & transcript data.\n  - Patches both database entries when an agent takes the ticket.\n", "operationId": "ctiDigital", "tags": ["Automated Tools"], "security": [{"OAuth2": []}], "parameters": [{"$ref": "#/components/parameters/transId"}, {"$ref": "#/components/parameters/transactionSrc"}], "requestBody": {"description": "Patch Virtual Assistant data in CTI Database from IVR.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CtiDigitalRequestV1"}, "example": {"vaConversationId": "0ee05b52-7e5b-4562-896e-005dd7235f7a", "vaCustIntent": "channelingstrategy_phone_domestictracking", "countryCode": "US", "infosourceTypeCode": "UI", "firstName": "CTI Test Data", "lastName": "CTI Test Data", "convDuration": 50, "ctiCallId": "08d6193e030320200804522160", "cbaRequestId": "36e011798a6cdc42dc96a32650226130a3", "cbaErrorCode": "WebCallbackFault", "languageCode": "EN", "phoneNum": "2015774078", "trackingNum": "1Z4175YY0394004806", "trackingNumType": "1Z", "packageStatusType": "1", "packageStatusCode": "A7", "activityLocaStatusCode": "01", "vaTranscript": "text up to 32000 chars", "lastPageVisited": "services/shippingpage", "destPostalCode": "07960", "callerType": "001", "myChoice": "Y", "callerCategory": "SH", "isInitialCall": "Y"}}}, "required": true}, "responses": {"200": {"description": "Successful operation", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CtiDigitalResponseV1"}, "example": {"cbaConfigId": "6", "ctiCallId": "08d6193e030320200804522160", "code": "00", "message": "Success"}}}}, "400": {"description": "Bad Request", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "Malformed JSON request."}, {"code": "TBD", "message": "Missing transId."}]}}}}}, "401": {"description": "Unauthorized", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "250002", "message": "Invalid Authentication Information."}]}}}}}, "404": {"description": "Resource Not Found", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "The requested <TBD> was not found."}]}}}}}, "500": {"description": "Server Error", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "Internal Server Error."}]}}}}}}}}, "/voice": {"post": {"summary": "Store Voice call data.", "description": "Stores Intelligent Voice Assistant call data into the CTI DB upon customer asks for an agent or IVA cannot process the query.", "operationId": "ctiVoice", "tags": ["Automated Tools"], "security": [{"OAuth2": []}], "parameters": [{"$ref": "#/components/parameters/transId"}, {"$ref": "#/components/parameters/transactionSrc"}], "requestBody": {"description": "To store transfer Call data from IVA to CTI.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CtiVoiceRequestV1"}, "example": {"proj": "AB", "uuid": "0a3c5088_00002d39_67d2276e_0565_0001", "ani": "2018287019", "dialnum": "0000069609", "givegetnum": "0071008702", "iata": "US", "lang": "EN", "ucid": "04FA08000577CF67D1CCF7", "infosourcetype": "R1", "callertype": "SH", "invoicedtidr": "N", "invoicefeetype": "LTPAY", "authstatus": "03", "callercategory": "008", "callercatgrp": "ND", "callerintent": "invoice_copies", "shipper": "A8918R", "callid": "ASNFSBUQ2025031207.41.28.5", "amzci": "0", "ovrsz": "N", "ameIdr": "0", "mychoiceidr": "1", "ivrd": "03122025", "ivrt": "14.51.29.560", "iatato": "US", "ti": "S", "rc": "00008", "ivrcustomerid": "1-1WRTCMWS", "customerprofileaddressId": "1-1WRTCMWS", "ivrServiceCode": "14", "internationalIdr": "D02", "pickupDate": "20252153", "callTime": "1312", "customerCloseTime": "US", "readyTime": "6Y0F88", "commitTime": "", "totalQuantity": "", "ivrCloseTime": "1", "shipperConfirmationNumber": "", "oversize": "N", "infonoticeNumber": "************", "locationType": "2", "accessPointZipCode": "91331", "dcrAction": "6J", "futureDate": "********", "returnStatusCode": "KV", "altPhone": "", "customerProfileShipperIATA": "", "newZipCode": "07306", "newCity": "DIBOLL", "newState": "CA", "newStreetNumber": "1000", "newStreetName": "248 Archer Ln", "newSecondaryInfo": "1312\"", "newIATA": "US", "geoCodeLatitude": "", "geoCodeLongitude": "", "pickupType": "01", "shippingLabelsIdr": "", "accountLocation": "N", "shipperAuthenicationIdr": "N", "customerProfileContactID": "1-1WRTCMWV", "destinationCount": "", "customerProfileIVRCustomerID": "1-1WRTCMWS", "paymendMethodType": "2", "originZipCode": "87108", "destinationZipCode": "29150", "residentialAddressIdr": "1", "dropOffPickupTime": "", "packageType": "2SR", "weight": "40", "length": "40", "width": "8", "height": "10", "billingRequestType": "3", "accountStatus": "", "phoneExtension": "", "middlewareReturnCode": "", "shipperAccountNumberArc": "x1192h", "trackingNumberType": "MI", "gsrEligibility": "", "invoiceAmount": "", "invoiceDate": "", "enrollmentStatus": "", "creditCardEligibility": "", "paymentType": "", "paymentMethodType": "", "packageActivityType": "", "packageActivityStatus": "", "mainMenuOption": "", "trackingnumber": ["1Z0AE288DA43450054", "1ZW933279008419414", "1ZW933279008419422"]}}}, "required": true}, "responses": {"200": {"description": "Successful operation", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CtiVoiceResponseV1"}, "example": {"icmind": "0", "routenum": "**********", "string": ""}}}}, "400": {"description": "Bad Request", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "Malformed JSON request."}, {"code": "TBD", "message": "Missing transId."}]}}}}}, "401": {"description": "Unauthorized", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "250002", "message": "Invalid Authentication Information."}]}}}}}, "404": {"description": "Resource Not Found", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "The requested <TBD> was not found."}]}}}}}, "500": {"description": "Server Error", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CTIErrorResponseV1"}, "example": {"response": {"errors": [{"code": "<TBD>", "message": "Internal Server Error."}]}}}}}}}}}, "components": {"securitySchemes": {"OAuth2": {"type": "oauth2", "description": "Find your Client ID and Secret on your app info page. 1. Select \"Try It\" 2. In the Security section enter your Client ID and Secret. 3. Select \"Request Token\" 4. Enter any additional information in the Body and Parameters sections. 5. Select \"Send\" to execute your API request\n", "flows": {"clientCredentials": {"x-tokenEndpointAuthMethod": "client_secret_basic", "tokenUrl": "https://onlinetools.ups.com/security/v1/oauth/token", "scopes": {}}}}}, "parameters": {"transId": {"name": "transId", "in": "header", "description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "maxLength": 32, "example": "abc1357924680xyz"}}, "transactionSrc": {"name": "transactionSrc", "in": "header", "description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "maxLength": 512, "default": "testing", "example": "TBD"}}}, "headers": {"BkndTransId": {"description": "The backend transaction id.", "required": true, "schema": {"type": "string", "example": "TRANSID123412421"}}, "transId": {"description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "minLength": 3, "maxLength": 36, "pattern": "^[a-zA-Z0-9-.]{3,36}$", "example": "0a1b9c2d8e3f7g4h6i5"}}, "transactionSrc": {"description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "minLength": 1, "maxLength": 36, "pattern": "^[a-zA-Z0-9-.]{1,36}$", "example": "UPS.com"}}, "APIErrorCode": {"description": "The API error code.", "required": true, "schema": {"type": "string", "example": "UJ0001"}}, "APIErrorMsg": {"description": "The API error message.", "required": true, "schema": {"type": "string", "example": "Invalid token or token is not present."}}}, "schemas": {"CtiDataRequestV1": {"type": "object", "description": "Container object for the CTI data request paload", "required": ["sCTICallID", "sRegionCode", "sLanguageCode", "sRequestTimestamp"], "properties": {"sCTICallID": {"type": "string", "example": "169fa059********1125146754", "maxLength": 26, "description": "* Mandatory Field"}, "sRegionCode": {"type": "string", "example": "NA", "maxLength": 2, "description": "* Mandatory Field"}, "sLanguageCode": {"type": "string", "example": "En", "maxLength": 2, "description": "* Mandatory Field"}, "sRequestTimestamp": {"type": "string", "example": "10/14/2021 13:36", "maxLength": 30, "description": "* Mandatory Field"}, "sAgentID": {"type": "string", "example": "1012052", "maxLength": 50, "description": "Optional"}, "sAgentName": {"type": "string", "example": "1012052", "maxLength": 20, "description": "Optional"}, "sAgentBuildingMnemonic": {"type": "string", "example": "Tenfold", "maxLength": 9, "description": "Optional"}, "sAgentExtension": {"type": "string", "example": "1011002", "maxLength": 7, "description": "Optional"}, "sAgentWorkstationName": {"type": "string", "example": "gbstustcc", "maxLength": 12, "description": "Optional"}, "sCallTransferIndicator": {"type": "string", "example": "0", "maxLength": 1, "description": "Optional"}, "sTransferAgentID": {"type": "string", "example": "", "maxLength": 50, "description": "Optional"}, "sTransferAgentName": {"type": "string", "example": "", "maxLength": 20, "description": "Optional"}, "sTransferAgentBuildingMnemonic": {"type": "string", "example": "", "maxLength": 9, "description": "Optional"}, "sTransferAgentExtension": {"type": "string", "example": "", "maxLength": 7, "description": "Optional"}, "sTransferAgentWorkstationName": {"type": "string", "example": "", "maxLength": 12, "description": "Optional"}, "sTransferDateTime": {"type": "string", "example": "", "maxLength": 30, "description": "Optional"}}}, "CtiDataResponseV1": {"allOf": [{"$ref": "#/components/schemas/CTIDataCommon"}, {"type": "object", "description": "Container object for the CTI data response paload", "properties": {"uuid": {"type": "string", "example": "169fa059-d44d-1578-899d-aa6889b55c11", "maxLength": 36, "description": "* Mandatory Field"}, "region": {"type": "string", "example": "NA", "maxLength": 2, "description": "* Mandatory Field"}, "app": {"type": "string", "example": "AT", "maxLength": 3, "description": "* Mandatory Field"}, "appDesc": {"type": "string", "example": "Domestic Tracking", "maxLength": 100, "description": "* System generated"}, "invoiceDateIdr": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "invoiceFeeType": {"type": "string", "maxLength": 5, "description": "* Optional Field"}, "invoiceFeeTypeDesc": {"type": "string", "maxLength": 250, "description": "* Optional Field"}, "billingRequestType": {"type": "string", "maxLength": 2, "description": "* Optional Field"}, "billingRequestTypeDesc": {"type": "string", "maxLength": 250, "description": "* Optional Field"}, "billAuthStatus": {"type": "string", "maxLength": 2, "description": "* Optional Field"}, "billAuthStatusDesc": {"type": "string", "maxLength": 250, "description": "* Optional Field"}, "accountStatus": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "accountStatusDesc": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "callerRole": {"type": "string", "example": "ND", "maxLength": 2, "description": "* Mandatory Field"}, "callerRoleDesc": {"type": "string", "example": "Not Determined", "maxLength": 20, "description": "* System generated"}, "callerCategory": {"type": "string", "example": "000", "maxLength": 5, "description": "* Mandatory Field"}, "callerCategoryDesc": {"type": "string", "example": "Not Categorized", "maxLength": 50, "description": "* System generated"}, "callerCategoryGrp": {"type": "string", "maxLength": 5, "description": "* Optional Field"}, "callerCategoryGrpDesc": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "callerIntent": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "ucid": {"type": "string", "maxLength": 36, "description": "* Optional Field"}, "ivrDate": {"type": "string", "example": "********", "maxLength": 8, "description": "* Mandatory Field (MMDDYYYY)"}, "ivrTime": {"type": "string", "example": "**********", "maxLength": 10, "description": "* Mandatory Field"}, "version": {"type": "string", "example": "0009", "maxLength": 4, "description": "* Mandatory Field"}, "transferInitiator": {"type": "string", "example": "S", "maxLength": 1, "description": "* Mandatory Field"}, "transferInitiatorDesc": {"type": "string", "example": "System Initiated", "maxLength": 50, "description": "* System generated"}, "reasonCode": {"type": "string", "example": "00001", "maxLength": 10, "description": "* Mandatory Field"}, "reasonCodeDesc": {"type": "string", "maxLength": 500, "description": "* Optional Field"}, "agentOpenTooltip": {"type": "string", "maxLength": 500, "description": "* Optional Field"}, "paymentMethodType": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "paymentMethodTypeDesc": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "paymentType": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "paymentTypeDesc": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "paymentOption": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "paymentOptionDesc": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "trackingNumber1": {"$ref": "#/components/schemas/CtiTrackingNumber"}, "trackingNumber2": {"$ref": "#/components/schemas/CtiTrackingNumber"}, "trackingNumber3": {"$ref": "#/components/schemas/CtiTrackingNumber"}, "shipperAccountNumber": {"type": "string", "maxLength": 6, "description": "* Optional Field"}, "phoneExtension": {"type": "string", "maxLength": 4, "description": "* Optional Field"}, "gsrEligibility": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "enrollmentStatus": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "creditCardEligibility": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "myChoiceTokenization": {"type": "string", "example": "1", "maxLength": 1, "description": "* Mandatory Field"}, "confirmationNumber": {"type": "string", "example": "", "maxLength": 30, "description": "* Optional Field"}, "overSizeIdr": {"type": "string", "maxLength": 20, "description": "* Optional Field"}, "ivrServiceCode": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "ivrServiceCodeDesc": {"type": "string", "maxLength": 50, "description": "* Optional Field"}, "pickupDate": {"type": "string", "maxLength": 15, "description": "* Optional Field (Format: MM/DD/YYYY or similar)"}, "pickupType": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "pickupTypeDesc": {"type": "string", "maxLength": 50, "description": "* Optional Field"}, "shippingLabelsIdr": {"type": "string", "maxLength": 20, "description": "* Optional Field"}, "accountLocation": {"type": "string", "maxLength": 50, "description": "* Optional Field"}, "shipperAuthIdr": {"type": "string", "maxLength": 20, "description": "* Optional Field"}, "customerCloseTime": {"type": "string", "maxLength": 15, "description": "* Optional Field"}, "ivrCloseTime": {"type": "string", "maxLength": 15, "description": "* Optional Field"}, "readyTime": {"type": "string", "maxLength": 15, "description": "* Optional Field"}, "commitTime": {"type": "string", "maxLength": 15, "description": "* Optional Field"}, "totalQuantity": {"type": "string", "maxLength": 5, "description": "* Optional Field"}, "destinationCount": {"type": "string", "maxLength": 5, "description": "* Optional Field"}, "newZipCode": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "newCity": {"type": "string", "maxLength": 50, "description": "* Optional Field"}, "newState": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "newIATA": {"type": "string", "maxLength": 5, "description": "* Optional Field"}, "newStreetNumber": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "newStreetName": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "newSecondaryInfo": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "customerProfileAddressId": {"type": "string", "maxLength": 30, "description": "* Optional Field"}, "customerProfileContactId": {"type": "string", "maxLength": 30, "description": "* Optional Field"}, "customerProfileShipperIATA": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "accountIATA": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "customerProfileIVRCustomerId": {"type": "string", "maxLength": 36, "description": "* Optional Field"}, "originZipCode": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "destZipCode": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "residentialAddressIdr": {"type": "string", "maxLength": 20, "description": "* Optional Field"}, "dropOffPickupType": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "dropOffPickupTypeDesc": {"type": "string", "maxLength": 50, "description": "* Optional Field"}, "agentTransferMsgs": {"type": "array", "items": {"type": "string"}, "description": "Unbounded array of transfer messages"}, "transferMsgTrackingNumber": {"type": "string", "maxLength": 50, "description": "* Optional Field"}, "lastTransferMsg": {"type": "string", "maxLength": 1000, "description": "* Optional Field"}, "weight": {"type": "string", "maxLength": 6, "description": "* Optional Field"}, "length": {"type": "string", "maxLength": 4, "description": "* Optional Field"}, "width": {"type": "string", "maxLength": 4, "description": "* Optional Field"}, "height": {"type": "string", "maxLength": 4, "description": "* Optional Field"}, "locationType": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "locationTypeDesc": {"type": "string", "maxLength": 50, "description": "* Optional Field"}, "geoCodeLatitude": {"type": "string", "maxLength": 10, "description": "* Optional Field (Geolocation Latitude)"}, "geoCodeLongitude": {"type": "string", "maxLength": 10, "description": "* Optional Field (Geolocation Longitude)"}, "internationalIdr": {"type": "string", "maxLength": 3, "description": "* Optional Field"}, "internationaIdrDesc": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "altPhone": {"type": "string", "maxLength": 10, "description": "* Optional Field"}, "dcrAction": {"type": "string", "maxLength": 2, "description": "* Optional Field"}, "dcrActionDesc": {"type": "string", "maxLength": 250, "description": "* Optional Field"}, "accessPointZipCode": {"type": "string", "maxLength": 5, "description": "* Optional Field"}, "packageActivityStatus": {"type": "string", "example": "OS", "maxLength": 10, "description": "* System generated"}, "packageActivityStatusDesc": {"type": "string", "example": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "maxLength": 250, "description": "* System generated"}, "amazonIdr": {"type": "string", "maxLength": 1, "description": "* Optional Field"}, "amazonIdrDesc": {"type": "string", "maxLength": 100, "description": "* Optional Field"}, "companyName": {"type": "string", "maxLength": 26, "description": "* Optional Field"}, "contactName": {"type": "string", "maxLength": 26, "description": "* Optional Field"}, "infoNoticeNumber": {"type": "string", "maxLength": 12, "description": "* Optional Field"}, "futureDate": {"type": "string", "maxLength": 8, "description": "* Optional Field"}, "returnStatusCode": {"type": "string", "maxLength": 2, "description": "* Optional Field"}, "returnStatusCodeDesc": {"type": "string", "maxLength": 250, "description": "* Optional Field"}, "invoiceAmount": {"type": "string", "maxLength": 11, "description": "* Optional Field"}, "invoiceDate": {"type": "string", "maxLength": 8, "description": "* Optional Field"}, "vaConversationId": {"type": "string", "example": "169fa059-d44d-1578-899d-aa6889b55c11", "maxLength": 36, "description": "* System generated"}, "heavyGoodsIdr": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "vaTranscript": {"type": "string", "example": "text up to 32000 chars", "maxLength": 32000, "description": "* Virtual Assistant transcript text"}, "lastWebPageVisited": {"type": "string", "example": "", "maxLength": 225, "description": "* Optional Field"}, "vaIntent": {"type": "string", "example": "channelingstrategy_phone_domestictracking", "maxLength": 100, "description": "* System generated intent"}, "vaIntentDesc": {"type": "string", "example": "Domestic Tracking", "maxLength": 50, "description": "* System generated"}, "firstName": {"type": "string", "example": "VA_FirstName", "maxLength": 40, "description": "* Optional Field"}, "lastName": {"type": "string", "example": "VA_LastName", "maxLength": 80, "description": "* Optional Field"}, "vaConversationDuration": {"type": "string", "example": "0", "maxLength": 2, "description": "* System generated duration in seconds"}, "callHistories": {"type": "array", "description": "Unbounded array of past CTI call history records", "items": {"allOf": [{"$ref": "#/components/schemas/CTIDataCommon"}, {"type": "object", "properties": {"seqNo": {"type": "string", "maxLength": 5, "example": "1", "description": "* Mandatory Field - Sequence number of the call (there will be multiple calls from the history)"}, "packageLocationStatus": {"type": "string", "maxLength": 5, "example": "01", "description": "* Optional Field - Current package location status code"}, "packageLocationStatusDesc": {"type": "string", "maxLength": 200, "example": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "description": "* Optional Field - Description of current package location status"}, "destPostalCode": {"type": "string", "maxLength": 10, "example": "", "description": "* Optional Field - Destination postal code"}, "myChoicePkgIdr": {"type": "string", "maxLength": 10, "example": "0", "description": "* Optional Field - MyChoice package ID reference"}}}]}}}, "additionalProperties": true}]}, "CtiDigitalRequestV1": {"type": "object", "description": "Container object for the CTI chatbot assistant request paload", "required": ["vaConversationId", "vaCustIntent", "countryCode", "infosourceTypeCode", "firstName", "lastName", "ctiCallId", "phoneNum"], "properties": {"vaConversationId": {"type": "string", "example": "0ee05b52-7e5b-4562-896e-005dd7235f7a", "maxLength": 36, "description": "* Mandatory Field"}, "vaCustIntent": {"type": "string", "example": "channelingstrategy_phone_domestictracking", "maxLength": 50, "description": "* Mandatory Field"}, "countryCode": {"type": "string", "example": "US", "maxLength": 2, "description": "* Mandatory Field"}, "infosourceTypeCode": {"type": "string", "example": "UI", "maxLength": 2, "description": "* Mandatory Field"}, "firstName": {"type": "string", "example": "CTI Test Data", "maxLength": 25, "description": "* Mandatory Field for update scenario"}, "lastName": {"type": "string", "example": "CTI Test Data", "maxLength": 25, "description": "* Mandatory Field for update scenario"}, "convDuration": {"type": "integer", "example": 50, "maxLength": 50, "description": "* Mandatory Field for update scenario"}, "ctiCallId": {"type": "string", "example": "08d6193e030320200804522160", "maxLength": 26, "description": "* Mandatory Field for update scenario"}, "cbaRequestId": {"type": "string", "example": "36e011798a6cdc42dc96a32650226130a3", "maxLength": 36, "description": "Not a Mandatory Field"}, "cbaErrorCode": {"type": "string", "example": "WebCallbackFault", "maxLength": 50, "description": "Not a Mandatory Field"}, "languageCode": {"type": "string", "example": "EN", "maxLength": 2, "description": "* Mandatory Field for initial call"}, "phoneNum": {"type": "string", "example": "2015774078", "maxLength": 40, "description": "* Mandatory Field for for update scenario"}, "trackingNum": {"type": "string", "example": "1Z4175YY0394004806", "maxLength": 30, "description": "Not a Mandatory Field"}, "trackingNumType": {"type": "string", "example": "1Z", "maxLength": 2, "description": "Not a Mandatory Field"}, "packageStatusType": {"type": "string", "example": "1", "maxLength": 1, "description": "Not a Mandatory Field"}, "packageStatusCode": {"type": "string", "example": "A7", "maxLength": 10, "description": "Not a Mandatory Field"}, "activityLocaStatusCode": {"type": "string", "example": "01", "maxLength": 4, "description": "Not a Mandatory Field"}, "vaTranscript": {"type": "string", "example": "text up to 32000 chars", "maxLength": 32000, "description": "Not a Mandatory Field"}, "lastPageVisited": {"type": "string", "example": "services/shippingpage", "maxLength": 255, "description": "Not a Mandatory Field"}, "destPostalCode": {"type": "string", "example": "07960", "maxLength": 16, "description": "Not a Mandatory Field"}, "callerType": {"type": "string", "example": "001", "maxLength": 3, "description": "Not a Mandatory Field"}, "myChoice": {"type": "string", "example": "Y", "maxLength": 1, "description": "Not a Mandatory Field"}, "callerCategory": {"type": "string", "example": "SH", "maxLength": 2, "description": "Not a Mandatory Field"}, "isInitialCall": {"type": "string", "example": "Y", "maxLength": 1, "description": "* Mandatory Field"}}, "additionalProperties": false}, "CtiDigitalResponseV1": {"type": "object", "description": "Container object for the CTI chatbot assistant response paload", "properties": {"cbaConfigId": {"type": "string", "example": "6", "maxLength": 2, "description": "Only present in the response for initial call"}, "ctiCallId": {"$ref": "#/components/schemas/CtiCallId"}}, "additionalProperties": false}, "CtiVoiceRequestV1": {"type": "object", "required": ["proj", "uuid", "ani", "dialnum", "givegetnum", "iata", "lang", "ucid", "infosourcetype", "callertype", "invoicedtidr", "invoicefeetype", "au<PERSON><PERSON>us", "callercategory", "callercatgrp", "callerintent", "shipper"], "properties": {"proj": {"type": "string", "example": "AB", "maxLength": 10, "description": "* Mandatory Field"}, "uuid": {"type": "string", "example": "0a3c5088_00002d39_67d2276e_0565_0001", "maxLength": 36, "description": "* Mandatory Field"}, "ani": {"type": "string", "example": "2018287019", "maxLength": 20, "description": "* Mandatory Field"}, "dialnum": {"type": "string", "example": "0000069609", "maxLength": 10, "description": "* Mandatory Field"}, "givegetnum": {"type": "string", "example": "0071008702", "maxLength": 10, "description": "* Mandatory Field"}, "iata": {"type": "string", "example": "US", "maxLength": 2, "description": "* Mandatory Field"}, "lang": {"type": "string", "example": "EN", "maxLength": 2, "description": "* Mandatory Field"}, "ucid": {"type": "string", "example": "04FA08000577CF67D1CCF7", "maxLength": 22, "description": "* Mandatory Field"}, "infosourcetype": {"type": "string", "example": "R1", "maxLength": 2, "description": "* Mandatory Field"}, "callertype": {"type": "string", "example": "SH", "maxLength": 2, "description": "* Mandatory Field"}, "invoicedtidr": {"type": "string", "example": "N", "maxLength": 1, "description": "* Mandatory Field"}, "invoicefeetype": {"type": "string", "example": "LTPAY", "maxLength": 10, "description": "* Mandatory Field"}, "authstatus": {"type": "string", "example": "03", "maxLength": 2, "description": "* Mandatory Field"}, "callercategory": {"type": "string", "example": "008", "maxLength": 3, "description": "* Mandatory Field"}, "callercatgrp": {"type": "string", "example": "ND", "maxLength": 2, "description": "* Mandatory Field"}, "callerintent": {"type": "string", "example": "invoice_copies", "maxLength": 40, "description": "* Mandatory Field"}, "shipper": {"type": "string", "example": "A8918R", "maxLength": 6, "description": "* Mandatory Field"}, "callid": {"type": "string", "example": "ASNFSBUQ2025031207.41.28.5", "maxLength": 26, "description": "* Optional Field"}, "amzci": {"type": "string", "example": "0", "maxLength": 1, "description": "* Optional Field"}, "ovrsz": {"type": "string", "example": "N", "maxLength": 1, "description": "* Optional Field"}, "ameIdr": {"type": "string", "example": "0", "maxLength": 1, "description": "* Optional Field"}, "mychoiceidr": {"type": "string", "example": "1", "maxLength": 2, "description": "* Optional Field"}, "ivrd": {"type": "string", "example": "03122025", "maxLength": 8, "description": "* Optional Field"}, "ivrt": {"type": "string", "example": "14.51.29.560", "maxLength": 12, "description": "* Optional Field"}, "iatato": {"type": "string", "example": "US", "maxLength": 2, "description": "* Optional Field"}, "ti": {"type": "string", "example": "S", "maxLength": 1, "description": "* Optional Field"}, "rc": {"type": "string", "example": "00008", "maxLength": 10, "description": "* Optional Field"}, "ivrcustomerid": {"type": "string", "example": "1-1WRTCMWS", "maxLength": 15, "description": "* Optional Field"}, "customerprofileaddressId": {"type": "string", "example": "1-1WRTCMWS", "maxLength": 15, "description": "* Optional Field"}, "ivrServiceCode": {"type": "string", "example": "14", "maxLength": 2, "description": "* Optional Field"}, "internationalIdr": {"type": "string", "example": "D02", "maxLength": 3, "description": "* Optional Field"}, "pickupDate": {"type": "string", "example": "20252153", "maxLength": 8, "description": "* Optional Field"}, "callTime": {"type": "string", "example": "1312", "maxLength": 8, "description": "* Optional Field"}, "customerCloseTime": {"type": "string", "example": "US", "maxLength": 8, "description": "* Optional Field"}, "readyTime": {"type": "string", "example": "6Y0F88", "maxLength": 8, "description": "* Optional Field"}, "commitTime": {"type": "string", "example": "", "maxLength": 9, "description": "* Optional Field"}, "totalQuantity": {"type": "string", "example": "", "maxLength": 4, "description": "* Optional Field"}, "ivrCloseTime": {"type": "string", "example": "1", "maxLength": 8, "description": "* Optional Field"}, "shipperConfirmationNumber": {"type": "string", "example": "", "maxLength": 12, "description": "* Optional Field"}, "oversize": {"type": "string", "example": "N", "maxLength": 1, "description": "* Optional Field"}, "infonoticeNumber": {"type": "string", "example": "************", "maxLength": 12, "description": "* Optional Field"}, "locationType": {"type": "string", "example": "2", "maxLength": 1, "description": "* Optional Field"}, "accessPointZipCode": {"type": "string", "example": "91331", "maxLength": 9, "description": "* Optional Field"}, "dcrAction": {"type": "string", "example": "6J", "maxLength": 2, "description": "* Optional Field"}, "futureDate": {"type": "string", "example": "********", "maxLength": 8, "description": "* Optional Field"}, "returnStatusCode": {"type": "string", "example": "KV", "maxLength": 2, "description": "* Optional Field"}, "altPhone": {"type": "string", "example": "", "maxLength": 20, "description": "* Optional Field"}, "customerProfileShipperIATA": {"type": "string", "example": "", "maxLength": 2, "description": "* Optional Field"}, "newZipCode": {"type": "string", "example": "07306", "maxLength": 9, "description": "* Optional Field"}, "newCity": {"type": "string", "example": "DIBOLL", "maxLength": 30, "description": "* Optional Field"}, "newState": {"type": "string", "example": "CA", "maxLength": 2, "description": "* Optional Field"}, "newStreetNumber": {"type": "string", "example": "1000", "maxLength": 10, "description": "* Optional Field"}, "newStreetName": {"type": "string", "example": "248 Archer Ln", "maxLength": 40, "description": "* Optional Field"}, "newSecondaryInfo": {"type": "string", "example": "1312", "maxLength": 8, "description": "* Optional Field"}, "newIATA": {"type": "string", "example": "US", "maxLength": 6, "description": "* Optional Field"}, "geoCodeLatitude": {"type": "string", "example": "", "maxLength": 10, "description": "* Optional Field"}, "geoCodeLongitude": {"type": "string", "example": "", "maxLength": 10, "description": "* Optional Field"}, "pickupType": {"type": "string", "example": "01", "maxLength": 2, "description": "* Optional Field"}, "shippingLabelsIdr": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "accountLocation": {"type": "string", "example": "N", "maxLength": 1, "description": "* Optional Field"}, "shipperAuthenicationIdr": {"type": "string", "example": "N", "maxLength": 1, "description": "* Optional Field"}, "customerProfileContactID": {"type": "string", "example": "1-1WRTCMWV", "maxLength": 15, "description": "* Optional Field"}, "destinationCount": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "customerProfileIVRCustomerID": {"type": "string", "example": "1-1WRTCMWS", "maxLength": 15, "description": "* Optional Field"}, "paymendMethodType": {"type": "string", "example": "2", "maxLength": 2, "description": "* Optional Field"}, "originZipCode": {"type": "string", "example": "87108", "maxLength": 9, "description": "* Optional Field"}, "destinationZipCode": {"type": "string", "example": "29150", "maxLength": 9, "description": "* Optional Field"}, "residentialAddressIdr": {"type": "string", "example": "1", "maxLength": 1, "description": "* Optional Field"}, "dropOffPickupTime": {"type": "string", "example": "", "maxLength": 2, "description": "* Optional Field"}, "packageType": {"type": "string", "example": "2SR", "maxLength": 3, "description": "* Optional Field"}, "weight": {"type": "string", "example": "40", "maxLength": 6, "description": "* Optional Field"}, "length": {"type": "string", "example": "40", "maxLength": 4, "description": "* Optional Field"}, "width": {"type": "string", "example": "8", "maxLength": 4, "description": "* Optional Field"}, "height": {"type": "string", "example": "10", "maxLength": 4, "description": "* Optional Field"}, "billingRequestType": {"type": "string", "example": "3", "maxLength": 2, "description": "* Optional Field"}, "accountStatus": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "phoneExtension": {"type": "string", "example": "", "maxLength": 4, "description": "* Optional Field"}, "middlewareReturnCode": {"type": "string", "example": "", "maxLength": 20, "description": "* Optional Field"}, "shipperAccountNumberArc": {"type": "string", "example": "x1192h", "maxLength": 6, "description": "* Optional Field"}, "trackingNumberType": {"type": "string", "example": "MI", "maxLength": 2, "description": "* Optional Field"}, "gsrEligibility": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "invoiceAmount": {"type": "string", "example": "", "maxLength": 20, "description": "* Optional Field"}, "invoiceDate": {"type": "string", "example": "", "maxLength": 8, "description": "* Optional Field"}, "enrollmentStatus": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "creditCardEligibility": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "paymentType": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "paymentMethodType": {"type": "string", "example": "", "maxLength": 1, "description": "* Optional Field"}, "packageActivityType": {"type": "string", "example": "PACKAGE HELD - PROCESSING DELAY", "maxLength": 36, "description": "* Optional Field"}, "packageActivityStatus": {"type": "string", "example": "", "maxLength": 2, "description": "* Optional Field"}, "mainMenuOption": {"type": "string", "example": "", "maxLength": 2, "description": "* Optional Field"}, "trackingnumber": {"type": "array", "description": "Unbounded array of tracking numbers", "items": {"type": "string", "maxLength": "30 // ittate throught the list for each field", "description": "* Optional Field Can hold multiple tracking numbers", "example": "1Z0AE288DA43450054"}, "example": ["1Z0AE288DA43450054", "1ZW933279008419414", "1ZW933279008419422"]}}, "additionalProperties": false}, "CtiVoiceResponseV1": {"type": "object", "properties": {"icmind": {"type": "string", "example": "0", "maxLength": 2, "description": "Only present in the response"}, "routenum": {"type": "string", "example": "**********", "maxLength": 10, "description": "Only present in the response"}, "message": {"type": "string", "example": "", "maxLength": 0, "description": "Only present in the response"}}, "additionalProperties": false}, "CtiCallId": {"type": "string", "maxLength": 36, "example": "169fa059032820251641165569", "description": "* Mandatory Field - Unique CTI Call ID"}, "CTIDataCommon": {"type": "object", "properties": {"ani": {"type": "string", "maxLength": 20, "example": "********", "description": "* Mandatory Field - Automatic Number Identification"}, "ctiCallId": {"$ref": "#/components/schemas/CtiCallId"}, "trackingNumber": {"$ref": "#/components/schemas/CtiTrackingNumber"}, "trackingNumberType": {"type": "string", "maxLength": 20, "example": "1Z", "description": "* Optional Field - Tracking number type code"}, "trackingNumberTypeDesc": {"type": "string", "maxLength": 50, "example": "Tracking", "description": "* Optional Field - Description of tracking number type"}, "packageActivityType": {"type": "string", "example": "PACKAGE HELD - PROCESSING DELAY", "maxLength": 36, "description": "* System generated"}, "packageActivityTypeDesc": {"type": "string", "maxLength": 100, "example": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "description": "* Optional Field - Description of activity type"}, "packageActivityStatus": {"type": "string", "maxLength": 2, "example": "OS", "description": "* Optional Field - Activity status code"}, "packageActivityStatusDesc": {"type": "string", "maxLength": 250, "example": "PACKAGE HELD DUE TO A PROCESSING DELAY IN HANDLING LITHIUM BATTERY SHIPMENTS", "description": "* Optional Field - Description of activity status"}, "callTime": {"type": "string", "maxLength": 25, "example": "2025-03-28 16:41:17.130", "description": "* Mandatory Field - Timestamp of the call"}, "repeatCallCount": {"type": "string", "maxLength": 2, "example": "1", "description": "* Optional Field - Number of times customer has called"}, "iata": {"type": "string", "maxLength": 5, "example": "US", "description": "* Optional Field - Origin IATA code"}, "iataTo": {"type": "string", "maxLength": 5, "example": "US", "description": "* Optional Field - Destination IATA code"}, "language": {"type": "string", "maxLength": 5, "example": "EN", "description": "* Optional Field - Language preference"}, "nextExpEventDate": {"type": "string", "maxLength": 15, "example": "2025-03-28", "description": "* Optional Field - Expected next event date"}, "nextExpEventTime": {"type": "string", "maxLength": 10, "example": "16-41-17", "description": "* Optional Field - Expected next event time"}, "isVADataIdr": {"type": "string", "maxLength": 1, "example": "1", "description": "* Optional Field - Virtual Assistant data indicator (0 or 1)"}, "cserEventId": {"type": "string", "maxLength": 50, "example": "CSER202503281641170653338", "description": "* Optional Field - Customer Service Event Record ID"}}, "additionalProperties": true}, "CtiTrackingNumber": {"type": "string", "maxLength": 75, "example": "1Z4175YY0394004855", "description": "* Optional Field - UPS Tracking Number"}, "CTIErrorResponseV1": {"type": "object", "description": "Response object for errors", "properties": {"response": {"description": "Response container to store errors", "type": "object", "properties": {"errors": {"type": "array", "description": "Unbounded array containing one or more error objects", "items": {"type": "object", "description": "Error entity", "properties": {"code": {"type": "string", "description": "error code", "minLength": 1}, "message": {"type": "string", "description": "error message", "minLength": 1}}, "required": ["code", "message"], "additionalProperties": false}}}, "required": ["errors"], "additionalProperties": false}}, "required": ["response"], "additionalProperties": false}}}}