{"openapi": "3.0.3", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://product-test.apps.uscentral1.clser-uat.gcp.ams1907.com/ccmw-billing", "description": "Generated server url"}], "paths": {"/api/v1/invoice-payment": {"post": {"tags": ["billing-rest-controller"], "operationId": "submitPayment", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingSubmitPaymentRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "400": {"description": "Bad Request", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "401": {"description": "Unauthorized", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "500": {"description": "Internal Server Error", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}}}}, "/api/v1/inquiry": {"post": {"tags": ["billing-rest-controller"], "operationId": "inquiry", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingInquiryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "400": {"description": "Bad Request", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "401": {"description": "Unauthorized", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "500": {"description": "Internal Server Error", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}}}}, "/api/v1/guaranteed-service-refund": {"post": {"tags": ["billing-rest-controller"], "operationId": "submitGSR", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingSubmitGSRRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "400": {"description": "Bad Request", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "401": {"description": "Unauthorized", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "500": {"description": "Internal Server Error", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}}}}, "/api/v1/call-termination": {"post": {"tags": ["billing-rest-controller"], "operationId": "endCall", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingEndCallRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "400": {"description": "Bad Request", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "401": {"description": "Unauthorized", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}, "500": {"description": "Internal Server Error", "headers": {"BkndTransId": {"$ref": "#/components/headers/BkndTransId"}, "TransId": {"$ref": "#/components/headers/transId"}, "transactionSrc": {"$ref": "#/components/headers/transactionSrc"}, "Content-Type": {"$ref": "#/components/headers/Content-Type"}, "APIErrorCode": {"$ref": "#/components/headers/APIErrorCode"}, "APIErrorMessage": {"$ref": "#/components/headers/APIErrorMsg"}}, "content": {"*/*": {"$ref": "#/components/schemas/ClientResponse"}}}}}}, "/api/v1/readyz": {"get": {"tags": ["billing-rest-controller"], "operationId": "getR<PERSON>y", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/healthz": {"get": {"tags": ["billing-rest-controller"], "operationId": "getHealth", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}}, "security": [{"AuthScheme": []}], "components": {"securitySchemes": {"AuthScheme": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "headers": {"BkndTransId": {"description": "The backend transaction id.", "schema": {"type": "string", "example": "383f7d397a48"}}, "transId": {"description": "An identifier unique to the request.", "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-.]{3,36}$", "minLength": 3, "maxLength": 36, "example": "0a1b9c2d8e3f7g4h6i5"}}, "transactionSrc": {"description": "Identifies the client/source application that is calling.", "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-.]{1,36}$", "minLength": 1, "maxLength": 36, "example": "UPS.com"}}, "Content-Type": {"description": "The Content-Type header provides the client with the actual content/media type of the returned content.", "schema": {"type": "string", "example": "application/json"}}, "APIErrorCode": {"description": "The API error code.", "schema": {"type": "string", "example": "UJ0001"}}, "APIErrorMsg": {"description": "The API error message.", "schema": {"type": "string", "example": "Invalid token or token is not present."}}}, "schemas": {"BillingSubmitPaymentRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "accountNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}, "selectedPaymentType": {"type": "string"}, "chargeCardType": {"type": "string"}, "chargeToken": {"type": "string"}, "bankRoutingNumber": {"type": "string"}, "chargeCardBilltoPostal": {"type": "string"}, "chargeCardSecurityCode": {"type": "string"}, "chargeCardExpirationDate": {"type": "string"}, "paymentMethodType": {"type": "string"}}, "required": ["accountNumber", "chargeCardBilltoPostal", "chargeCardExpirationDate", "chargeCardSecurityCode", "chargeCardType", "chargeToken", "countryCode", "paymentMethodType", "selectedPaymentType", "sequenceNumber", "transId", "transactionCode"]}, "BillingInquiryRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "phoneNumber": {"type": "string"}, "accountNumber": {"type": "string"}, "trackingNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "sequenceNumber", "transId", "transactionCode"]}, "BillingSubmitGSRRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "phoneNumber": {"type": "string"}, "phoneExtension": {"type": "string"}, "trackingNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "phoneNumber", "sequenceNumber", "trackingNumber", "transId", "transactionCode"]}, "BillingEndCallRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "sequenceNumber", "transId", "transactionCode"]}, "ClientResponse": {"type": "object", "properties": {"billingResponse": {"$ref": "#/components/schemas/BillingResponse"}, "errorResponse": {"$ref": "#/components/schemas/ErrorResponse"}}}, "BillingResponse": {"type": "object", "properties": {"phoneNumber": {"type": "string"}, "trackingNumber": {"type": "string"}, "gsrEligible": {"type": "string"}, "requestedForPhoneNumber": {"type": "string"}, "lastAllowedGSR": {"type": "string"}, "phoneNumbertoConfirm": {"type": "string"}, "phoneExtensiontoConfirm": {"type": "string"}, "enrolledInbillingCenter": {"type": "string"}, "accountNumber": {"type": "string"}, "totalBalanceDue": {"type": "string"}, "totalBalancePastDue": {"type": "string"}, "balanceAction": {"type": "string"}, "confirmationNumber": {"type": "string"}, "ctiPassCode": {"type": "string"}, "transferPhone": {"type": "string"}, "blindTransfer": {"type": "string"}, "transferPhoneInternational": {"type": "string"}, "blindTransferInternational": {"type": "string"}, "commonResponse": {"$ref": "#/components/schemas/CommonResponse"}, "callCenterHoursofOperation": {"$ref": "#/components/schemas/CallCenterHoursofOperation"}, "invoices": {"type": "array", "maxItems": 20, "items": {"$ref": "#/components/schemas/Invoice"}}, "payments": {"type": "array", "maxItems": 20, "items": {"$ref": "#/components/schemas/Payment"}}}}, "CommonResponse": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}, "returnCode": {"type": "string"}, "returncodeDescription": {"type": "string"}}}, "CallCenterHoursofOperation": {"type": "object", "properties": {"isValid": {"type": "string"}, "mondaytoFridayAMOpenTime": {"type": "string"}, "mondaytoFridayAMCloseTime": {"type": "string"}, "mondaytoFridayPMOpenTime": {"type": "string"}, "mondaytoFridayPMCloseTime": {"type": "string"}, "saturdayAMOpenTime": {"type": "string"}, "saturdayAMCloseTime": {"type": "string"}, "saturdayPMOpenTime": {"type": "string"}, "saturdayPMCloseTime": {"type": "string"}, "sundayAMOpenTime": {"type": "string"}, "sundayAMCloseTime": {"type": "string"}, "sundayPMOpenTime": {"type": "string"}, "sundayPMCloseTime": {"type": "string"}}}, "Account": {"type": "object", "properties": {"isBillable": {"type": "string"}, "status": {"type": "string"}}}, "Invoice": {"type": "object", "properties": {"invoicedate": {"type": "string"}}}, "Payment": {"type": "object", "properties": {"paymentDate": {"type": "string"}, "paymentAmount": {"type": "string"}}}, "ErrorResponse": {"type": "object", "properties": {"response": {"$ref": "#/components/schemas/Response"}}}, "Response": {"type": "object", "properties": {"errors": {"type": "array", "maxItems": 20, "items": {"$ref": "#/components/schemas/Error"}}}}, "Error": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}}}}}