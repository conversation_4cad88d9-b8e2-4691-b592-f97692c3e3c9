{"openapi": "3.1.0", "info": {"title": "Customer 360 Account API v1", "description": "The Customer 360 Account API provides a unified interface for retrieving account-related information within the Customer 360 ecosystem. \nIt enables clients to search and retrieve details about customer accounts associated with shipping or pickup services.\nThis API is designed to enhance visibility into customer data, support personalized interactions, and optimize account management processes.\n", "version": "1.0"}, "tags": [{"name": "Accounts", "description": "Account related operations"}], "servers": [{"url": "https://onlinetools.ups.com", "description": "Production"}, {"url": "https://onlinetoolsuat.ups.com", "description": "UAT"}, {"url": "https://onlinetoolspt.ups.com", "description": "PT"}], "security": [{"oauth2": []}], "paths": {"/account/search": {"post": {"operationId": "searchAccounts", "tags": ["Accounts"], "summary": "Retrieve customer accounts based on search criteria", "description": "This endpoint allows clients to search for and retrieve account details based on specific criteria such as phone number, country code, or account number. It returns matched accounts with relevant attributes, including account identifiers, contact details, and location information.\n", "security": [{"oauth2": []}], "parameters": [{"$ref": "#/components/parameters/transId"}, {"$ref": "#/components/parameters/transactionSrc"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRequest"}, "examples": {"SearchByPhoneAndCountryFilterByPriority": {"value": {"searchCriteria": {"searchType": "PhoneAndCountry", "phoneNumber": "***********", "countryCode": "US", "filterConditions": ["singleAccountByPriority"]}}}, "SearchByPhoneAndCountryAll": {"value": {"searchCriteria": {"searchType": "PhoneAndCountry", "phoneNumber": "***********", "countryCode": "US"}}}, "SearchByAccountIncludePaymentAuthorization": {"value": {"searchCriteria": {"searchType": "Account", "accountNumber": "111111", "fields": ["accountPaymentAuthorization"]}}}, "SearchByAccountIncludeAll": {"value": {"searchCriteria": {"searchType": "Account", "accountNumber": "111111", "fields": ["all"]}}}}}}}, "responses": {"200": {"description": "Successful retrieval account profile information based on a search mode", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountSearchResponse"}, "examples": {"successfulResponseWithNoDataExample": {"value": {"totalResults": 0, "timeStamp": "2025-01-06T10:05:06.086Z", "accounts": []}}, "successfulResponseByFilterDataExample": {"value": {"totalResults": 1, "timeStamp": "2025-01-03T10:05:06.086Z", "accounts": [{"accountStatusCode": "A", "accountName": "ACCT1", "accountNumber": "********", "accountGroups": ["Preferred Customer"], "accountTypeCode": "01", "addresses": [{"addressTypeCode": "GN", "line1": "1000 Main St.", "line2": "Apt. 305", "line3": "Suite 24", "streetNumber": "1000", "streetName": "Main", "streetAddressTypeCode": "ST", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}, {"addressTypeCode": "PU", "line1": "105 E Riverside RD", "streetNumber": "105", "streetName": "Riverside", "streetPrefixName": "E", "streetAddressTypeCode": "RD", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}], "contacts": [{"name": "<PERSON>", "phoneNumber": "**********", "email": "<EMAIL>", "contactTypeCode": "GN"}, {"phoneNumber": "**********", "contactTypeCode": "PU"}], "pickup": {"pickupInstruction": "Instruction 1", "customerUpsNotifyByTime": "13.30.00", "saturdayCustomerUpsNotifyByTime": "11.30.00", "pickupPlanFrequencyTypeCode": "01"}, "paymentAuthorization": {"activeAccountIndicator": true, "billableAccountIndicator": true, "shipperPayForRSIndicator": true, "shipperPayForNonRSIndicator": true, "shipperAddressCountryCode": "US"}}]}}, "successfulResponseAllDataExample": {"value": {"totalResults": 2, "timeStamp": "2025-01-03T10:05:06.086Z", "accounts": [{"accountStatusCode": "A", "accountName": "ACCT1", "accountNumber": "********", "accountGroups": ["Preferred Customer", "UPS Store"], "accountTypeCode": "01", "addresses": [{"addressTypeCode": "GN", "line1": "1000 Main St.", "line2": "Apt. 305", "line3": "Suite 24", "streetNumber": "1000", "streetName": "Main", "streetAddressTypeCode": "ST", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}, {"addressTypeCode": "PU", "line1": "105 E Riverside RD", "streetNumber": "105", "streetName": "Riverside", "streetPrefixName": "E", "streetAddressTypeCode": "RD", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}], "contacts": [{"name": "<PERSON>", "phoneNumber": "**********", "email": "<EMAIL>", "contactTypeCode": "PU"}], "pickup": {"pickupInstruction": "Instruction 1", "customerUpsNotifyByTime": "13.30.00", "saturdayCustomerUpsNotifyByTime": "11.30.00", "pickupPlanFrequencyTypeCode": "01"}, "paymentAuthorization": {"activeAccountIndicator": true, "billableAccountIndicator": true, "shipperPayForRSIndicator": true, "shipperPayForNonRSIndicator": true, "shipperAddressCountryCode": "US"}}, {"accountStatusCode": "A", "accountName": "ACCT2", "accountNumber": "********", "accountTypeCode": "09", "addresses": [{"addressTypeCode": "GN", "line1": "456 Riverside RD", "streetNumber": "456", "streetName": "Riverside", "streetAddressTypeCode": "RD", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}], "contacts": [{"name": "<PERSON>", "phoneNumber": "**********", "email": "<EMAIL>", "contactTypeCode": "PU"}], "paymentAuthorization": {"activeAccountIndicator": false, "billableAccountIndicator": false, "shipperPayForRSIndicator": false, "shipperPayForNonRSIndicator": false, "shipperAddressCountryCode": "US"}}]}}, "successfulResponseForAccountNumberSearchIncludePaymentAuthorizationDataExample": {"value": {"totalResults": 1, "timeStamp": "2025-01-03T10:05:06.086Z", "accounts": [{"paymentAuthorization": {"activeAccountIndicator": true, "billableAccountIndicator": true, "shipperPayForRSIndicator": true, "shipperPayForNonRSIndicator": true, "shipperAddressCountryCode": "US"}}]}}, "successfulResponseForAccountNumberSearchIncludeAll": {"value": {"totalResults": 1, "timeStamp": "2025-01-03T10:05:06.086Z", "accounts": [{"accountStatusCode": "A", "accountName": "ACCT1", "accountNumber": "********", "accountGroups": ["Preferred Customer", "UPS Store"], "accountTypeCode": "01", "addresses": [{"addressTypeCode": "GN", "line1": "1000 Main St.", "line2": "Apt. 305", "line3": "Suite 24", "streetNumber": "1000", "streetName": "Main", "streetAddressTypeCode": "ST", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}, {"addressTypeCode": "PU", "line1": "105 E Riverside RD", "streetNumber": "105", "streetName": "Riverside", "streetPrefixName": "E", "streetAddressTypeCode": "RD", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}], "contacts": [{"name": "<PERSON>", "phoneNumber": "**********", "email": "<EMAIL>", "contactTypeCode": "PU"}], "pickup": {"pickupInstruction": "Instruction 1", "customerUpsNotifyByTime": "13.30.00", "saturdayCustomerUpsNotifyByTime": "11.30.00", "pickupPlanFrequencyTypeCode": "01"}, "paymentAuthorization": {"activeAccountIndicator": true, "billableAccountIndicator": true, "shipperPayForRSIndicator": true, "shipperPayForNonRSIndicator": true, "shipperAddressCountryCode": "US"}}]}}}}}}, "400": {"description": "Validation/Request Error", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "ERR_VAL_001", "message": "Missing required field {field name}"}]}}}}}, "401": {"description": "Unauthorized", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "250002", "message": "Invalid OAuth2 Token"}]}}}}}}}}, "/account/{accountnumber}": {"get": {"operationId": "fetchAccount", "tags": ["Accounts"], "summary": "Retrieve customer accounts based on search criteria", "description": "Fetches account details based on the provided account number and country. The combination of account number and country code should make unique identifier for an account.\n", "security": [{"oauth2": []}], "parameters": [{"$ref": "#/components/parameters/transId"}, {"$ref": "#/components/parameters/transactionSrc"}, {"name": "accountnumber", "in": "path", "description": "Account number", "required": true, "schema": {"$ref": "#/components/schemas/Account"}}, {"name": "country_code", "in": "query", "description": "Country code", "required": true, "schema": {"$ref": "#/components/schemas/CountryCode"}}], "responses": {"200": {"description": "Successful retrieval account profile information based on account number and country code.", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountGetResponse"}, "examples": {"successfulResponseDataExample": {"value": {"totalResults": 1, "timeStamp": "2025-01-03T10:05:06.086Z", "account": {"accountStatusCode": "A", "accountName": "ACCT1", "accountNumber": "********", "accountGroups": ["Preferred Customer"], "accountTypeCode": "01", "addresses": [{"addressTypeCode": "GN", "line1": "1000 Main St.", "line2": "Apt. 305", "line3": "Suite 24", "streetNumber": "1000", "streetName": "Main", "streetAddressTypeCode": "ST", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}, {"addressTypeCode": "PU", "line1": "105 E Riverside RD", "streetNumber": "105", "streetName": "Riverside", "streetPrefixName": "E", "streetAddressTypeCode": "RD", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}], "contacts": [{"name": "<PERSON>", "phoneNumber": "**********", "email": "<EMAIL>", "contactTypeCode": "PU"}], "pickup": {"pickupInstruction": "Instruction 1", "customerUpsNotifyByTime": "13.30.00", "saturdayCustomerUpsNotifyByTime": "11.30.00", "pickupPlanFrequencyTypeCode": "01"}, "paymentAuthorization": {"activeAccountIndicator": true, "billableAccountIndicator": true, "shipperPayForRSIndicator": true, "shipperPayForNonRSIndicator": true, "shipperAddressCountryCode": "US"}}}}}}}}, "400": {"description": "Validation/Request Error", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "ERR_VAL_001", "message": "Missing required field {field name}"}]}}}}}, "401": {"description": "Unauthorized", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "250002", "message": "Invalid OAuth2 Token"}]}}}}}, "404": {"description": "Not Found", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "ERR_GEN_001", "message": "Account not found"}]}}}}}}}}, "/account": {"put": {"operationId": "createOrUpdateAccountProfile", "tags": ["Accounts"], "summary": "Create or update the singleton account profile for pickup purposes.", "description": "Creates a new account profile if one does not exist, or updates the existing account profile. The created account will systematically have accountTypeCode set to NA. If an account exists, updates are only allowed when accountTypeCode is NA. If the accountTypeCode is not NA, the request will be rejected with a 422 Unprocessable Entity response.\n", "parameters": [{"$ref": "#/components/parameters/transId"}, {"$ref": "#/components/parameters/transactionSrc"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCreateRequest"}, "example": {"address": {"streetNumber": "123", "streetName": "Riverside", "streetPrefixName": "E", "streetSuffixName": "NE", "streetAddressTypeCode": "DR", "buildingFloorNumber": "7A", "roomSuiteNumber": "305", "cityName": "Atlanta", "stateProvinceCode": "GA", "postalCode": "30303", "countryCode": "US"}, "phoneNumber": "***********"}}}}, "responses": {"200": {"description": "Successful update of account profile.", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCreateResponse"}, "example": {"totalResults": 1, "timeStamp": "2025-01-03T10:05:06.086Z", "account": {"accountStatusCode": "A", "accountTypeCode": "NA", "addresses": [{"addressTypeCode": "PU", "line1": "123 E Riverside NE DR", "line2": "Bld. 7A", "line3": "Suite 305", "streetNumber": "123", "streetName": "Riverside", "streetPrefixName": "E", "streetSuffixName": "NE", "streetAddressTypeCode": "DR", "buildingFloorNumber": "7A", "roomSuiteNumber": "305", "cityName": "Atlanta", "stateProvinceCode": "GA", "postalCode": "30303", "countryCode": "US"}], "contacts": [{"phoneNumber": "***********"}, {"contactTypeCode": "PU"}]}}}}}, "201": {"description": "Successful creation of account profile.", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountCreateResponse"}, "example": {"totalResults": 1, "timeStamp": "2025-01-03T10:05:06.086Z", "account": {"accountStatusCode": "A", "accountTypeCode": "NA", "addresses": [{"addressTypeCode": "PU", "line1": "123 E Riverside NE DR", "line2": "Bld. 7A", "line3": "Suite 305", "streetNumber": "123", "streetName": "Riverside", "streetPrefixName": "E", "streetSuffixName": "NE", "streetAddressTypeCode": "DR", "buildingFloorNumber": "7A", "roomSuiteNumber": "305", "cityName": "Atlanta", "stateProvinceCode": "GA", "postalCode": "30303", "countryCode": "US"}], "contacts": [{"phoneNumber": "***********", "contactTypeCode": "PU"}]}}}}}, "400": {"description": "Validation/Request Error. 400 error could return when required field such as transId, transactionSrc, or UUID is not received.", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "ERR_VAL_001", "message": "Missing required field {field name}"}]}}}}}, "401": {"description": "Unauthorized", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "250002", "message": "Invalid OAuth2 Token"}]}}}}}, "422": {"description": "Unprocessable request", "headers": {"bkndtransid": {"$ref": "#/components/headers/BkndTransId"}, "transId": {"$ref": "#/components/headers/TransId"}, "apierrorcode": {"$ref": "#/components/headers/ApiErrorCode"}, "apierrormsg": {"$ref": "#/components/headers/ApiErrorMsg"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"response": {"errors": [{"code": "ERR_ACCT_001", "message": "The account status is not eligible for update."}]}}}}}}}}}, "components": {"securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"clientCredentials": {"x-tokenEndpointAuthMethod": "client_secret_basic", "tokenUrl": "/security/v1/oauth/token", "scopes": {}}}}, "JWTAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "parameters": {"transId": {"name": "transId", "in": "header", "description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-]{3,36}$", "minLength": 3, "maxLength": 36, "example": "XZ345445668"}}, "transactionSrc": {"name": "transactionSrc", "in": "header", "description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-_]{3,30}$", "minLength": 3, "maxLength": 30, "example": "IVR"}}}, "headers": {"BkndTransId": {"description": "The backend transaction id, for gateway purpose.", "schema": {"type": "string", "maxLength": 36}}, "TransId": {"description": "The transaction id received from client", "schema": {"type": "string", "maxLength": 36}}, "ApiErrorCode": {"description": "The API error code.", "schema": {"type": "string", "example": "UJ0001"}}, "ApiErrorMsg": {"description": "The API error message.", "schema": {"type": "string", "example": "Invalid token or token is not present."}}}, "schemas": {"BaseAddress": {"type": "object", "description": "Base address entity that includes city, state, country, postal, and address type.", "properties": {"addressTypeCode": {"description": "Address code.\n- **PU** - Pickup\n- **GN** - General\n", "type": "string", "enum": ["PU", "GN"]}, "cityName": {"description": "Name of city.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "postalCodeExtension": {"description": "Postal code extension.", "maxLength": 5, "example": "3655"}, "countryCode": {"$ref": "#/components/schemas/CountryCode"}}}, "StreetAddress": {"description": "Street address entity that includes line1, line2, etc.", "type": "object", "required": ["line1"], "properties": {"line1": {"description": "Street number line 1.", "type": "string", "maxLength": 100, "example": "1000 Main St."}, "line2": {"description": "Floor or apartment number.", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Floor or apartment number.", "type": "string", "maxLength": 100, "example": "Apt. 305"}}}, "ParsedStreetAddress": {"description": "Parsed street address entity that includes street number, street name, etc.", "type": "object", "required": ["streetNumber", "streetName"], "properties": {"streetNumber": {"description": "Street number.", "type": "string", "maxLength": 20, "example": "1000"}, "streetName": {"description": "Floor or apartment number.", "type": "string", "maxLength": 50, "example": "Main St."}, "streetPrefixName": {"description": "Street name prefix.", "type": "string", "maxLength": 20, "example": "E"}, "streetSuffixName": {"description": "Street name suffix.", "type": "string", "maxLength": 20, "example": "NE"}, "streetAddressTypeCode": {"description": "Street address type code such as AVE, ST, DR, BLVD", "type": "string", "example": "AVE"}, "buildingFloorNumber": {"description": "Building / Floor number.", "type": "string", "maxLength": 3, "example": "123"}, "roomSuiteNumber": {"description": "Room / Suite number.", "type": "string", "maxLength": 8, "example": "2550"}}}, "Account": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}, "Phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "PhoneAndCountryCriteria": {"description": "Schema for PhoneAndCountry search mode", "type": "object", "required": ["searchType", "phoneNumber", "countryCode"], "properties": {"searchType": {"type": "string", "enum": ["PhoneAndCountry"]}, "phoneNumber": {"$ref": "#/components/schemas/Phone"}, "countryCode": {"$ref": "#/components/schemas/CountryCode"}, "filterConditions": {"$ref": "#/components/schemas/FilterConditions"}}}, "AccountCriteria": {"description": "Schema for Account search mode. This search mode requires to have include paymentAuthorization in the request payload. Response will only contain paymentAuthorization section.", "type": "object", "required": ["searchType", "accountNumber"], "properties": {"searchType": {"type": "string", "enum": ["Account"]}, "accountNumber": {"$ref": "#/components/schemas/Account"}, "filterConditions": {"$ref": "#/components/schemas/FilterConditions"}, "fields": {"$ref": "#/components/schemas/AccountNumberSearchInclude"}}}, "SearchRequest": {"description": "Search criteria options", "type": "object", "required": ["searchCriteria"], "properties": {"searchCriteria": {"description": "Search criteria", "type": "object", "oneOf": [{"$ref": "#/components/schemas/PhoneAndCountryCriteria"}, {"$ref": "#/components/schemas/AccountCriteria"}]}}}, "AccountNumberSearchInclude": {"description": "Specify what data set(s) to be included in response. If value is not defined API will accountPaymentAuthorization as the default.\nCurrently only accountPaymentAuthorization is allowed when search only by account number.\n- **accountPaymentAuthorization** - will only return payment authorization summary.\n- **all** - will return full account profile\n", "type": "array", "default": ["all"], "items": {"type": "string", "enum": ["accountPaymentAuthorization", "all"]}}, "FilterConditions": {"description": "Filter conditions for response. \nsingleAccountByPriority - will return the account with highest priority. \nall - will return all the accounts matched the search criteria. \nIf filterConditions is not provided, default value is all.\n", "type": "array", "items": {"type": "string", "enum": ["singleAccountByPriority", "all"]}}, "AccountCreateRequest": {"description": "Request entity for account profile creation.", "type": "object", "required": ["address", "phoneNumber"], "properties": {"address": {"description": "Parsed street address entity that includes street number, street name, etc.\nBase address entity that includes city, state, country, postal, and address type.", "type": "object", "required": ["streetName", "streetNumber"], "properties": {"streetNumber": {"description": "Street number.", "type": "string", "maxLength": 20, "example": "1000"}, "streetName": {"description": "Floor or apartment number.", "type": "string", "maxLength": 50, "example": "Main St."}, "streetPrefixName": {"description": "Street name prefix.", "type": "string", "maxLength": 20, "example": "E"}, "streetSuffixName": {"description": "Street name suffix.", "type": "string", "maxLength": 20, "example": "NE"}, "streetAddressTypeCode": {"description": "Street address type code such as AVE, ST, DR, BLVD", "type": "string", "example": "AVE"}, "buildingFloorNumber": {"description": "Building / Floor number.", "type": "string", "maxLength": 3, "example": "123"}, "roomSuiteNumber": {"description": "Room / Suite number.", "type": "string", "maxLength": 8, "example": "2550"}, "addressTypeCode": {"description": "Address code.\n- **PU** - Pickup\n- **GN** - General\n", "type": "string", "enum": ["PU", "GN"]}, "cityName": {"description": "Name of city.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "postalCodeExtension": {"description": "Postal code extension.", "maxLength": 5, "example": "3655"}, "countryCode": {"$ref": "#/components/schemas/CountryCode"}}}, "phoneNumber": {"$ref": "#/components/schemas/Phone"}}}, "AccountSearchResponse": {"description": "Base Response object", "type": "object", "required": ["totalResults", "timeStamp"], "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"$ref": "#/components/schemas/Warning"}}, "accounts": {"type": "array", "maxItems": 20, "items": {"$ref": "#/components/schemas/AccountProfile"}}}}, "AccountGetResponse": {"description": "Base Response object", "type": "object", "required": ["totalResults", "timeStamp"], "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"$ref": "#/components/schemas/Warning"}}, "account": {"$ref": "#/components/schemas/AccountProfile"}}}, "AccountCreateResponse": {"description": "Base Response object", "type": "object", "required": ["totalResults", "timeStamp"], "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"$ref": "#/components/schemas/Warning"}}, "account": {"$ref": "#/components/schemas/AccountProfile"}}}, "BaseResponse": {"description": "Base Response object", "type": "object", "required": ["totalResults", "timeStamp"], "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"$ref": "#/components/schemas/Warning"}}}}, "AccountProfile": {"description": "Response entity for account profile.", "type": "object", "properties": {"accountStatusCode": {"description": "Account status code.\n- **A** - Active\n- **I** - Inactive\n- **U** - Suspended\n- **D** - Canceled\n- **P** - Pending\n", "type": "string", "enum": ["A", "I", "U", "D", "P"]}, "accountName": {"description": "Business name of the customer / company on the account.", "type": "string", "maxLength": 30, "example": "ABC"}, "accountNumber": {"$ref": "#/components/schemas/Account"}, "accountGroups": {"description": "Account group.", "type": "array", "items": {"type": "string", "enum": ["Health Care", "UPS Store", "Amazon Preferred Shippers", "Restricted Shippers (no changes allowed, no DCO, no DCRs)", "Preferred Customer"]}}, "accountCountryCode": {"description": "Country code for the account.", "$ref": "#/components/schemas/CountryCode"}, "accountSystemNumber": {"description": "Internal system number for account, required during pickup submission.", "type": "string", "pattern": "^[0-9]*$", "maxLength": 10, "example": "270486"}, "accountTypeCode": {"description": "Account type code.\n  - **NA** - Non-Account Profile\n  - **01** - Daily Pickup\n  - **02** - Drop Shipper\n  - **03** - UPS Customer Counter\n  - **04** - Occasional Pickup\n  - **05** - Drop-Direct Return\n  - **06** - One Time Pickup\n  - **07** - B.I.N. Customer\n  - **08** - Temp B.I.N. Customer \n  - **09** - Receiver Only\n  - **10** - Commercial Counter\n  - **11** - Authorized Shipping Outlet\n  - **12** - ESISS\n  - **15** - Web Seller\n  - **16** - Importer Occasional\n  - **17** - UPS Store\n  - **18** - UPS Designated Location \n  - **19** - Indoor Letter Center\n  - **20** - Air Service Center\n  - **21** - Outdoor Letter Center\n  - **22** - Customer Group              \n  - **24** - ULAN (User Level Account (non-billable))\n  - **60** - Non-Shipping\n  - **61** - Cash Only\n  - **62** - Temporary\n  - **63** - Import Occasional\n", "type": "string", "maxLength": 2, "example": "01"}, "addresses": {"description": "Addresses associated with the account.", "type": "array", "maxItems": 10, "items": {"description": "Base address entity that includes city, state, country, postal, and address type.\nStreet address entity that includes line1, line2, etc.\nParsed street address entity that includes street number, street name, etc.", "type": "object", "required": ["line1", "streetName", "streetNumber"], "properties": {"addressTypeCode": {"description": "Address code.\n- **PU** - Pickup\n- **GN** - General\n", "type": "string", "enum": ["PU", "GN"]}, "cityName": {"description": "Name of city.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "postalCodeExtension": {"description": "Postal code extension.", "maxLength": 5, "example": "3655"}, "countryCode": {"$ref": "#/components/schemas/CountryCode"}, "line1": {"description": "Street number line 1.", "type": "string", "maxLength": 100, "example": "1000 Main St."}, "line2": {"description": "Floor or apartment number.", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Floor or apartment number.", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "streetNumber": {"description": "Street number.", "type": "string", "maxLength": 20, "example": "1000"}, "streetName": {"description": "Floor or apartment number.", "type": "string", "maxLength": 50, "example": "Main St."}, "streetPrefixName": {"description": "Street name prefix.", "type": "string", "maxLength": 20, "example": "E"}, "streetSuffixName": {"description": "Street name suffix.", "type": "string", "maxLength": 20, "example": "NE"}, "streetAddressTypeCode": {"description": "Street address type code such as AVE, ST, DR, BLVD", "type": "string", "example": "AVE"}, "buildingFloorNumber": {"description": "Building / Floor number.", "type": "string", "maxLength": 3, "example": "123"}, "roomSuiteNumber": {"description": "Room / Suite number.", "type": "string", "maxLength": 8, "example": "2550"}}}}, "contacts": {"description": "List of contacts of the account.", "type": "array", "items": {"type": "object", "properties": {"name": {"description": "Full name of the contact.", "type": "string", "maxLength": 30, "example": "<PERSON>"}, "phoneNumber": {"description": "Contact phone number.", "$ref": "#/components/schemas/Phone"}, "email": {"description": "Email of the contact.", "type": "string", "format": "email", "example": "<EMAIL>"}, "contactTypeCode": {"description": "Contact type code.\n- **PU** - Pickup\n- **GN** - General\n", "type": "string", "enum": ["PU", "GN"]}}}}, "pickup": {"$ref": "#/components/schemas/AccountPickupPreferences"}, "paymentAuthorization": {"$ref": "#/components/schemas/AccountPaymentAuthorization"}}}, "AccountPickupPreferences": {"description": "Pickup preferences set for the account.", "type": "object", "properties": {"pickupInstruction": {"description": "Instruction for pickup.", "type": "string", "maxLength": 256, "example": "Instruction 1"}, "customerUpsNotifyByTime": {"description": "Weekday time that customer should call UPS by in order to schedule the pickup for the day.", "type": "string", "maxLength": 8, "example": "13.30.00"}, "saturdayCustomerUpsNotifyByTime": {"description": "Saturday time that customer should call UPS by in order to schedule the pickup for the day.", "type": "string", "maxLength": 8}, "pickupPlanFrequencyTypeCode": {"description": "Pickup frequency set for the account.\n- **00** - Non-Account\n- **01** - Pickup Daily\n- **02** - Pickup When Needed\n- **03** - Pickup one day per week\n- **04** - Pickup two days per week\n- **05** - Pickup three days per week\n- **06** - Pickup four days per week\n- **07** - Pickup Scheduled On Delivery\n- **08** - No Pickup Plan                \n", "type": "string", "maxLength": 2, "example": "01"}}}, "AccountPaymentAuthorization": {"description": "Shipper account authorization regarding the payment.", "type": "object", "properties": {"activeAccountIndicator": {"description": "Indicates if the account is an active account.", "type": "boolean", "example": true}, "billableAccountIndicator": {"description": "Indicates if the account is a billable account.", "type": "boolean", "example": true}, "shipperPayForRSIndicator": {"description": "Shipper pays for Return Service (RS).", "type": "boolean", "example": true}, "shipperPayForNonRSIndicator": {"description": "Shipper pays for non Return Service (non-RS) such as forward.", "type": "boolean", "example": true}, "shipperAddressCountryCode": {"description": "Country code for shipper address", "$ref": "#/components/schemas/CountryCode"}}}, "CountryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[A-Z]{2}$", "example": "US"}, "ErrorResponse": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"$ref": "#/components/schemas/Error"}}}}}}, "Error": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code.", "examples": ["00400"]}, "message": {"type": "string", "description": "The error message.", "examples": ["Bad Request"]}}}, "Warning": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "WRN_ACCT_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched accounts exceeds 20"}}}}}}