{"openapi": "3.0.3", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://product-test.apps.uscentral1.clser-uat.gcp.ams1907.com", "description": "Generated server url"}], "paths": {"/api/v1/invoice-payment": {"post": {"tags": ["billing-rest-controller"], "operationId": "submitPayment", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "accountNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}, "selectedPaymentType": {"type": "string"}, "chargeCardType": {"type": "string"}, "chargeToken": {"type": "string"}, "bankRoutingNumber": {"type": "string"}, "chargeCardBilltoPostal": {"type": "string"}, "chargeCardSecurityCode": {"type": "string"}, "chargeCardExpirationDate": {"type": "string"}, "paymentMethodType": {"type": "string"}}, "required": ["accountNumber", "chargeCardBilltoPostal", "chargeCardExpirationDate", "chargeCardSecurityCode", "chargeCardType", "chargeToken", "countryCode", "paymentMethodType", "selectedPaymentType", "sequenceNumber", "transId", "transactionCode"]}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {}}}}}}}, "/api/v1/inquiry": {"post": {"tags": ["billing-rest-controller"], "operationId": "inquiry", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "phoneNumber": {"type": "string"}, "accountNumber": {"type": "string"}, "trackingNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "sequenceNumber", "transId", "transactionCode"]}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {}}}}}}}, "/api/v1/guaranteed-service-refund": {"post": {"tags": ["billing-rest-controller"], "operationId": "submitGSR", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "phoneNumber": {"type": "string"}, "phoneExtension": {"type": "string"}, "trackingNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "phoneNumber", "sequenceNumber", "trackingNumber", "transId", "transactionCode"]}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {}}}}}}}, "/api/v1/call-termination": {"post": {"tags": ["billing-rest-controller"], "operationId": "endCall", "parameters": [{"name": "transid", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionSrc", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Content-Type", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "sequenceNumber", "transId", "transactionCode"]}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {}}}}}}}, "/api/v1/readyz": {"get": {"tags": ["billing-rest-controller"], "operationId": "getR<PERSON>y", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/healthz": {"get": {"tags": ["billing-rest-controller"], "operationId": "getHealth", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}}, "security": [{"AuthScheme": []}], "components": {"securitySchemes": {"AuthScheme": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"BillingSubmitPaymentRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "accountNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}, "selectedPaymentType": {"type": "string"}, "chargeCardType": {"type": "string"}, "chargeToken": {"type": "string"}, "bankRoutingNumber": {"type": "string"}, "chargeCardBilltoPostal": {"type": "string"}, "chargeCardSecurityCode": {"type": "string"}, "chargeCardExpirationDate": {"type": "string"}, "paymentMethodType": {"type": "string"}}, "required": ["accountNumber", "chargeCardBilltoPostal", "chargeCardExpirationDate", "chargeCardSecurityCode", "chargeCardType", "chargeToken", "countryCode", "paymentMethodType", "selectedPaymentType", "sequenceNumber", "transId", "transactionCode"]}, "BillingInquiryRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "phoneNumber": {"type": "string"}, "accountNumber": {"type": "string"}, "trackingNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "sequenceNumber", "transId", "transactionCode"]}, "BillingSubmitGSRRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "phoneNumber": {"type": "string"}, "phoneExtension": {"type": "string"}, "trackingNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "phoneNumber", "sequenceNumber", "trackingNumber", "transId", "transactionCode"]}, "BillingEndCallRequest": {"type": "object", "properties": {"sequenceNumber": {"type": "string"}, "transactionCode": {"type": "string"}, "transId": {"type": "string"}, "requestedTimeStamp": {"type": "string"}, "countryCode": {"type": "string"}, "actionCode": {"type": "string"}}, "required": ["countryCode", "sequenceNumber", "transId", "transactionCode"]}}}}