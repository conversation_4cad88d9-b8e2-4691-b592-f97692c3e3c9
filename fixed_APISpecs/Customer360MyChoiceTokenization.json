{"openapi": "3.0.3", "info": {"title": "Customer 360 Consignee API v1", "description": "The Customer360 Consignee API is designed to provide eligible one time passcode (OTP) email and phone options, as well as existing verification status.\n  - **Current package manifest consignee data**: email and phone number provided in the manifest for the shipment being searched for.\n  - **History of related packages' manifest consignee data**: utilizes token, addressToken, or stopId to find related shipments and return any emails or phone numbers provided in the manifest.\n  - **Ability to update Lexis Nexis verification status**: When the information (name, address, email) is verified with Lexis Nexis by an application the status is updated to Customer360 to be included in future API responses.\n  - **Ability to update teleSign status**: When an application verifies with TeleSign that a phone number is a mobile number and the billing address matches the package, the status is updated to Customer360 to be included in future API responses.\n  - **Ability to update OTP status**: When a user completes OTP process successfully, the status is updated to Customer360 to be included in future API responses.\n", "version": "1.0"}, "tags": [{"name": "Consignee", "description": "Get or update one time passcode (OTP) options for a package recipient. Includes email and/or phone number from manifest, as well as matched UPS.com profile email and phone number and My Choice for Home enrollment email and phone number."}], "servers": [{"url": "https://onlinetools.ups.com", "description": "Production"}, {"url": "https://onlinetoolsuat.ups.com", "description": "UAT"}, {"url": "https://onlinetoolspt.ups.com", "description": "PT"}], "security": [{"oauth2": []}], "paths": {"/api/customer360/v1/privileged/consignee/search": {"post": {"operationId": "searchRecipient", "tags": ["Consignee"], "summary": "Search package recipients", "description": "This endpoint provides clients ability to search consignee records using a list of pre-defined search criteria.\n", "parameters": [{"name": "transId", "in": "header", "description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-]{3,36}$", "minLength": 3, "maxLength": 36, "example": "XZ345445668"}}, {"name": "transactionSrc", "in": "header", "description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-_]{3,30}$", "minLength": 3, "maxLength": 30, "example": "FCTC"}}, {"name": "id_token", "in": "header", "required": false, "description": "id token which will be required for auth0", "schema": {"type": "string", "format": "jwt"}}], "x-slo": {"response_time": [{"percentile": 99, "value": "{response time value in milliseconds}"}, {"percentile": 95, "value": "{response time value in milliseconds}"}]}, "requestBody": {"content": {"application/json": {"schema": {"description": "Search request schema for recipient search", "type": "object", "required": ["searchCriteria"], "properties": {"searchCriteria": {"description": "Schema for token search criteria, includes CPA token, addressToken and stopId values\nSchema for NameAndAddress search mode\nSchema for tracking number search criteria", "type": "object", "properties": {"searchType": {"type": "string", "enum": ["token"]}, "token": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}, "name": {"type": "string", "example": "<PERSON>"}, "address": {"description": "", "type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}, "required": ["countryCode", "line1"]}, "trackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}}}, "include": {"description": "List of data elements to be included in response.  Profile is not included by default, it is optional.\nIf includeAll is specified entire list of phone numbers and emails will return, including the records that have LexisNexis verification status as \"FAIL\".\n", "type": "array", "items": {"type": "string", "enum": ["profile", "lastAddedEmail", "lastVerifiedEmail", "lastAddedPhoneNumber", "lastVerifiedPhoneNumber", "includeAll"]}}}}}}}, "responses": {"200": {"description": "Successful retrieval of consignee's record", "headers": {"application/json": {"schema": {"description": "The headers that are returned for all error responses.", "type": "object", "required": ["bkndtransid", "transId"], "properties": {"bkndtransid": {"description": "The backend transaction id, for gateway purpose. bkntransid will have same value as transId if transId is received from client, otherwise random ID will be generated.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}}}}}, "content": {"application/json": {"schema": {"description": "Base Response object", "type": "object", "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "RN_CUST_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched customers exceeds 50"}}}}, "recipients": {"type": "array", "items": {"description": "Recipient data entity", "type": "object", "required": ["token", "status"], "properties": {"token": {"description": "UPS consignee token; includes values for My choice address token, CPA (consignee physical address) token, or stopId when no other tokens apply", "type": "string"}, "name": {"type": "string", "example": "<PERSON>"}, "address": {"type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}}, "trackingContact": {"description": "Tracking contact is only returned when 1) API is called to search by Tracking Number and 2) there's at lease an email or a phone number associated with the tracking number. \nThis data property will contain the email / phone information associated with the specified tracking number.\n", "type": "object", "required": ["trackingNumber"], "minProperties": 2, "properties": {"trackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}}}, "profile": {"description": "UPS.com user profile information.  Only applicable to registered users", "type": "object", "properties": {"userIds": {"description": "List of user id information associated with this recipient", "type": "array", "items": {"type": "object", "required": ["userId", "userRegistrationId", "uuid", "email"], "properties": {"userId": {"description": "UPS.com user id", "type": "string", "minLength": 2, "maxLength": 36, "pattern": "^[a-zA-Z0-9\\-]+$", "example": "shoesLtdTrackUPS"}, "userRegistrationId": {"description": "Registration number for ups.com user", "type": "string", "maxLength": 10, "pattern": "^[a-zA-Z]{2}[0-9]{8}$", "example": "IR00033000"}, "uuid": {"description": "UUID for ups.com user", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9\\-]+$", "example": "4ce27ff5-8e17-4797-a8c1-089796dbed9c"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}, "myChoiceEnrollments": {"description": "List of My Choice for Home enrollments associated with this recipient", "type": "array", "items": {"type": "object", "required": ["addressToken", "email"], "properties": {"addressToken": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}}}, "phoneList": {"description": "Phone list will be sorted by the \"dateAdded\" attribute for each phone number. The latest one first.", "type": "array", "items": {"type": "object", "maxItems": 20, "required": ["phoneNumber"], "properties": {"phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the phone number was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedNumber": {"description": "Indicates whether the phone number is the most recently verified successfully for the recipient. A value of true means it is the most recent verified number, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of phone number data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "teleSignVerification": {"description": "Used to determine if the provided phone number is a mobile or landline number.", "type": "object", "required": ["phoneType", "dateTime"], "properties": {"phoneType": {"description": "Telesign determination of phone number category", "type": "string", "enum": ["LANDLINE", "MOBILE", "UNKNOWN"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for telesign verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "addressMatchCode": {"description": "Indicates if the address provided by telesign response matches the address associated with the package or MyChoice enrollment.", "type": "string", "enum": ["MATCHED", "NOT_MATCHED", "NOT_APPLICABLE"]}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the phone", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "emailList": {"description": "Email list will be sorted by the \"dateAdded\" attribute for each email. The latest one first.", "type": "array", "maxItems": 20, "items": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the email was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedEmail": {"description": "Indicates whether the email is the most recently verified successfully for the recipient. A value of true means it is the most recent verified email, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of email data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the email", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "status": {"description": "Status of the recipient record.", "type": "string", "enum": ["active", "inactive"]}}}}}, "required": ["totalResults", "timeStamp"]}}}}, "400": {"description": "Validation/Request Error. 400 error could return when required field such as transId, transactionSrc, or UUID is not received.", "headers": {"application/json": {"schema": {"description": "The headers that are returned from the backend and gateway", "required": ["bkndtransid", "apierrorcode", "apie<PERSON><PERSON>g"], "properties": {"bkndtransid": {"description": "The backend transaction id.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}, "apierrorcode": {"description": "The API error code.", "type": "string", "example": "UJ0001"}, "apierrormsg": {"description": "The API error message.", "type": "string", "example": "Invalid token or token is not present."}}}}}, "content": {"application/json": {"schema": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "example": {"response": {"errors": [{"code": "ERR_VAL_001", "message": "Missing required field {field name}"}]}}}}}, "401": {"description": "Unauthorized", "headers": {"application/json": {"schema": {"description": "The headers that are returned from the backend and gateway", "required": ["bkndtransid", "apierrorcode", "apie<PERSON><PERSON>g"], "properties": {"bkndtransid": {"description": "The backend transaction id.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}, "apierrorcode": {"description": "The API error code.", "type": "string", "example": "UJ0001"}, "apierrormsg": {"description": "The API error message.", "type": "string", "example": "Invalid token or token is not present."}}}}}, "content": {"application/json": {"schema": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "example": {"response": {"errors": [{"code": "UJ0001", "message": "Invalid token or token is not present"}]}}}}}}}}, "/api/customer360/v1/privileged/consignee": {"patch": {"operationId": "updateRecipient", "tags": ["Consignee"], "summary": "Update consignee verification status", "description": "This endpoint provides clients ability to update the verification status for an existing consignee record.\n", "parameters": [{"name": "transId", "in": "header", "description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-]{3,36}$", "minLength": 3, "maxLength": 36, "example": "XZ345445668"}}, {"name": "transactionSrc", "in": "header", "description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-_]{3,30}$", "minLength": 3, "maxLength": 30, "example": "FCTC"}}, {"name": "id_token", "in": "header", "required": false, "description": "id token which will be required for auth0", "schema": {"type": "string", "format": "jwt"}}], "x-slo": {"response_time": [{"percentile": 99, "value": "{response time value in milliseconds}"}, {"percentile": 95, "value": "{response time value in milliseconds}"}]}, "requestBody": {"content": {"application/json": {"schema": {"description": "Request entity for recipient update", "type": "object", "required": ["token"], "minProperties": 2, "properties": {"token": {"description": "UPS consignee physical address token", "type": "string", "example": "053cbff582e24fad8aba5b3f0436e768"}, "emailVerification": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}}}, "phoneVerification": {"type": "object", "required": ["phoneNumber"], "properties": {"phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "teleSignVerification": {"description": "Used to determine if the provided phone number is a mobile or landline number.", "type": "object", "required": ["phoneType", "dateTime"], "properties": {"phoneType": {"description": "Telesign determination of phone number category", "type": "string", "enum": ["LANDLINE", "MOBILE", "UNKNOWN"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for telesign verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "addressMatchCode": {"description": "Indicates if the address provided by telesign response matches the address associated with the package or MyChoice enrollment.", "type": "string", "enum": ["MATCHED", "NOT_MATCHED", "NOT_APPLICABLE"]}}}}}}}}}}, "responses": {"200": {"description": "Successful update of consignee's verification record", "headers": {"application/json": {"schema": {"description": "The headers that are returned for all error responses.", "type": "object", "required": ["bkndtransid", "transId"], "properties": {"bkndtransid": {"description": "The backend transaction id, for gateway purpose. bkntransid will have same value as transId if transId is received from client, otherwise random ID will be generated.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}}}}}, "content": {"application/json": {"schema": {"description": "Base Response object", "type": "object", "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "RN_CUST_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched customers exceeds 50"}}}}, "recipients": {"type": "array", "items": {"description": "Recipient data entity", "type": "object", "required": ["token", "status"], "properties": {"token": {"description": "UPS consignee token; includes values for My choice address token, CPA (consignee physical address) token, or stopId when no other tokens apply", "type": "string"}, "name": {"type": "string", "example": "<PERSON>"}, "address": {"type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}}, "trackingContact": {"description": "Tracking contact is only returned when 1) API is called to search by Tracking Number and 2) there's at lease an email or a phone number associated with the tracking number. \nThis data property will contain the email / phone information associated with the specified tracking number.\n", "type": "object", "required": ["trackingNumber"], "minProperties": 2, "properties": {"trackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}}}, "profile": {"description": "UPS.com user profile information.  Only applicable to registered users", "type": "object", "properties": {"userIds": {"description": "List of user id information associated with this recipient", "type": "array", "items": {"type": "object", "required": ["userId", "userRegistrationId", "uuid", "email"], "properties": {"userId": {"description": "UPS.com user id", "type": "string", "minLength": 2, "maxLength": 36, "pattern": "^[a-zA-Z0-9\\-]+$", "example": "shoesLtdTrackUPS"}, "userRegistrationId": {"description": "Registration number for ups.com user", "type": "string", "maxLength": 10, "pattern": "^[a-zA-Z]{2}[0-9]{8}$", "example": "IR00033000"}, "uuid": {"description": "UUID for ups.com user", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9\\-]+$", "example": "4ce27ff5-8e17-4797-a8c1-089796dbed9c"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}, "myChoiceEnrollments": {"description": "List of My Choice for Home enrollments associated with this recipient", "type": "array", "items": {"type": "object", "required": ["addressToken", "email"], "properties": {"addressToken": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}}}, "phoneList": {"description": "Phone list will be sorted by the \"dateAdded\" attribute for each phone number. The latest one first.", "type": "array", "items": {"type": "object", "maxItems": 20, "required": ["phoneNumber"], "properties": {"phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the phone number was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedNumber": {"description": "Indicates whether the phone number is the most recently verified successfully for the recipient. A value of true means it is the most recent verified number, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of phone number data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "teleSignVerification": {"description": "Used to determine if the provided phone number is a mobile or landline number.", "type": "object", "required": ["phoneType", "dateTime"], "properties": {"phoneType": {"description": "Telesign determination of phone number category", "type": "string", "enum": ["LANDLINE", "MOBILE", "UNKNOWN"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for telesign verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "addressMatchCode": {"description": "Indicates if the address provided by telesign response matches the address associated with the package or MyChoice enrollment.", "type": "string", "enum": ["MATCHED", "NOT_MATCHED", "NOT_APPLICABLE"]}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the phone", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "emailList": {"description": "Email list will be sorted by the \"dateAdded\" attribute for each email. The latest one first.", "type": "array", "maxItems": 20, "items": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the email was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedEmail": {"description": "Indicates whether the email is the most recently verified successfully for the recipient. A value of true means it is the most recent verified email, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of email data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the email", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "status": {"description": "Status of the recipient record.", "type": "string", "enum": ["active", "inactive"]}}}}}, "required": ["totalResults", "timeStamp"]}, "example": {"totalResults": 1, "timeStamp": "2024-04-06T10:05:06Z", "recipients": [{"token": "053cbff582e24fad8aba5b3f0436e768", "name": "<PERSON>", "address": {"line1": "1000 Main St.", "line2": "Apt. 305", "line3": "Suite 24", "municipalityName": "LOUISVILLE", "stateProvinceCode": "KY", "postalCode": "40207", "countryCode": "US"}, "phoneList": [{"phoneNumber": "+18778377456", "dataSource": "GRV", "lexisNexisVerification": {"status": "FAIL", "dateTime": "2024-04-04T10:05:06Z", "reasonType": "Shipping Number", "reason": "1ZC1F972YW39085316"}, "teleSignVerification": {"phoneType": "MOBILE", "dateTime": "2024-04-04T10:05:06Z", "reasonType": "Shipping Number", "reason": "1ZC1F972YW39085316", "addressMatchCode": "MATCHED"}, "otpVerification": {"status": "PASS", "dateTime": "2024-04-04T10:05:06Z", "reasonType": "Shipping Number", "reason": "1ZC1F972YW39085316"}}], "emailList": [{"email": "<EMAIL>", "dataSource": "GRV", "lexisNexisVerification": {"status": "PASS", "dateTime": "2024-04-04T10:05:06Z", "reasonType": "Shipping Number", "reason": "1ZC1F972YW39085316"}, "otpVerification": {"status": "PASS", "dateTime": "2024-04-04T10:05:06Z", "reasonType": "Shipping Number", "reason": "1ZC1F972YW39085316"}}], "status": "active"}]}}}}, "400": {"description": "Validation/Request Error. 400 error could return when required field such as transId, transactionSrc, or UUID is not received.", "headers": {"application/json": {"schema": {"description": "The headers that are returned from the backend and gateway", "required": ["bkndtransid", "apierrorcode", "apie<PERSON><PERSON>g"], "properties": {"bkndtransid": {"description": "The backend transaction id.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}, "apierrorcode": {"description": "The API error code.", "type": "string", "example": "UJ0001"}, "apierrormsg": {"description": "The API error message.", "type": "string", "example": "Invalid token or token is not present."}}}}}, "content": {"application/json": {"schema": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "example": {"response": {"errors": [{"code": "ERR_VAL_001", "message": "Missing required field {field name}"}]}}}}}, "401": {"description": "Unauthorized", "headers": {"application/json": {"schema": {"description": "The headers that are returned from the backend and gateway", "required": ["bkndtransid", "apierrorcode", "apie<PERSON><PERSON>g"], "properties": {"bkndtransid": {"description": "The backend transaction id.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}, "apierrorcode": {"description": "The API error code.", "type": "string", "example": "UJ0001"}, "apierrormsg": {"description": "The API error message.", "type": "string", "example": "Invalid token or token is not present."}}}}}, "content": {"application/json": {"schema": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "example": {"response": {"errors": [{"code": "UJ0001", "message": "Invalid token or token is not present"}]}}}}}, "404": {"description": "Recipient Not Found", "headers": {"application/json": {"schema": {"description": "The headers that are returned from the backend and gateway", "required": ["bkndtransid", "apierrorcode", "apie<PERSON><PERSON>g"], "properties": {"bkndtransid": {"description": "The backend transaction id.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}, "apierrorcode": {"description": "The API error code.", "type": "string", "example": "UJ0001"}, "apierrormsg": {"description": "The API error message.", "type": "string", "example": "Invalid token or token is not present."}}}}}, "content": {"application/json": {"schema": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}}}}}}}}, "components": {"securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"clientCredentials": {"x-tokenEndpointAuthMethod": "client_secret_basic", "tokenUrl": "/security/v1/oauth/token", "scopes": {}}}}, "JWTAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "parameters": {"transId": {"name": "transId", "in": "header", "description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-]{3,36}$", "minLength": 3, "maxLength": 36, "example": "XZ345445668"}}, "transactionSrc": {"name": "transactionSrc", "in": "header", "description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-_]{3,30}$", "minLength": 3, "maxLength": 30, "example": "FCTC"}}, "idToken": {"name": "id_token", "in": "header", "required": false, "description": "id token which will be required for auth0", "schema": {"type": "string", "format": "jwt"}}}, "schemas": {"UserUUID": {"description": "UUID for ups.com user", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9\\-]+$", "example": "4ce27ff5-8e17-4797-a8c1-089796dbed9c"}, "UserID": {"description": "UPS.com user id", "type": "string", "minLength": 2, "maxLength": 36, "pattern": "^[a-zA-Z0-9\\-]+$", "example": "shoesLtdTrackUPS"}, "UserRegistrationNumber": {"description": "Registration number for ups.com user", "type": "string", "maxLength": 10, "pattern": "^[a-zA-Z]{2}[0-9]{8}$", "example": "IR00033000"}, "Name": {"type": "string", "example": "<PERSON>"}, "Address": {"type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}}, "Email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "EmailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "Phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "PhoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}, "Token": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}, "TeleSignVerification": {"description": "Used to determine if the provided phone number is a mobile or landline number.", "type": "object", "required": ["phoneType", "dateTime"], "properties": {"phoneType": {"description": "Telesign determination of phone number category", "type": "string", "enum": ["LANDLINE", "MOBILE", "UNKNOWN"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for telesign verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "addressMatchCode": {"description": "Indicates if the address provided by telesign response matches the address associated with the package or MyChoice enrollment.", "type": "string", "enum": ["MATCHED", "NOT_MATCHED", "NOT_APPLICABLE"]}}}, "OTPVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "LexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "NameAndAddressCriteria": {"description": "Schema for NameAndAddress search mode", "type": "object", "required": ["searchType", "name", "address"], "properties": {"searchType": {"type": "string", "enum": ["NameAndAddress"]}, "name": {"type": "string", "example": "<PERSON>"}, "address": {"description": "", "type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}, "required": ["countryCode", "line1"]}}}, "TokenCriteria": {"description": "Schema for token search criteria, includes CPA token, addressToken and stopId values", "type": "object", "required": ["searchType", "token"], "properties": {"searchType": {"type": "string", "enum": ["token"]}, "token": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}}}, "TrackingNumberCriteria": {"description": "Schema for tracking number search criteria", "type": "object", "required": ["searchType", "trackingNumber"], "properties": {"searchType": {"type": "string", "enum": ["TrackingNumber"]}, "trackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}}}, "RecipientSearchRequest": {"description": "Search request schema for recipient search", "type": "object", "required": ["searchCriteria"], "properties": {"searchCriteria": {"description": "Schema for token search criteria, includes CPA token, addressToken and stopId values\nSchema for NameAndAddress search mode\nSchema for tracking number search criteria", "type": "object", "properties": {"searchType": {"type": "string", "enum": ["token"]}, "token": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}, "name": {"type": "string", "example": "<PERSON>"}, "address": {"description": "", "type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}, "required": ["countryCode", "line1"]}, "trackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}}}, "include": {"description": "List of data elements to be included in response.  Profile is not included by default, it is optional.\nIf includeAll is specified entire list of phone numbers and emails will return, including the records that have LexisNexis verification status as \"FAIL\".\n", "type": "array", "items": {"type": "string", "enum": ["profile", "lastAddedEmail", "lastVerifiedEmail", "lastAddedPhoneNumber", "lastVerifiedPhoneNumber", "includeAll"]}}}}, "RecipientUpdateRequest": {"description": "Request entity for recipient update", "type": "object", "required": ["token"], "minProperties": 2, "properties": {"token": {"description": "UPS consignee physical address token", "type": "string", "example": "053cbff582e24fad8aba5b3f0436e768"}, "emailVerification": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}}}, "phoneVerification": {"type": "object", "required": ["phoneNumber"], "properties": {"phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "teleSignVerification": {"description": "Used to determine if the provided phone number is a mobile or landline number.", "type": "object", "required": ["phoneType", "dateTime"], "properties": {"phoneType": {"description": "Telesign determination of phone number category", "type": "string", "enum": ["LANDLINE", "MOBILE", "UNKNOWN"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for telesign verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "addressMatchCode": {"description": "Indicates if the address provided by telesign response matches the address associated with the package or MyChoice enrollment.", "type": "string", "enum": ["MATCHED", "NOT_MATCHED", "NOT_APPLICABLE"]}}}}}}}, "BaseResponse": {"description": "Base Response object", "type": "object", "required": ["totalResults", "timeStamp"], "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "RN_CUST_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched customers exceeds 50"}}}}}}, "TrackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}, "RecipientsResponse": {"description": "Base Response object", "type": "object", "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "RN_CUST_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched customers exceeds 50"}}}}, "recipients": {"type": "array", "items": {"description": "Recipient data entity", "type": "object", "required": ["token", "status"], "properties": {"token": {"description": "UPS consignee token; includes values for My choice address token, CPA (consignee physical address) token, or stopId when no other tokens apply", "type": "string"}, "name": {"type": "string", "example": "<PERSON>"}, "address": {"type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}}, "trackingContact": {"description": "Tracking contact is only returned when 1) API is called to search by Tracking Number and 2) there's at lease an email or a phone number associated with the tracking number. \nThis data property will contain the email / phone information associated with the specified tracking number.\n", "type": "object", "required": ["trackingNumber"], "minProperties": 2, "properties": {"trackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}}}, "profile": {"description": "UPS.com user profile information.  Only applicable to registered users", "type": "object", "properties": {"userIds": {"description": "List of user id information associated with this recipient", "type": "array", "items": {"type": "object", "required": ["userId", "userRegistrationId", "uuid", "email"], "properties": {"userId": {"description": "UPS.com user id", "type": "string", "minLength": 2, "maxLength": 36, "pattern": "^[a-zA-Z0-9\\-]+$", "example": "shoesLtdTrackUPS"}, "userRegistrationId": {"description": "Registration number for ups.com user", "type": "string", "maxLength": 10, "pattern": "^[a-zA-Z]{2}[0-9]{8}$", "example": "IR00033000"}, "uuid": {"description": "UUID for ups.com user", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9\\-]+$", "example": "4ce27ff5-8e17-4797-a8c1-089796dbed9c"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}, "myChoiceEnrollments": {"description": "List of My Choice for Home enrollments associated with this recipient", "type": "array", "items": {"type": "object", "required": ["addressToken", "email"], "properties": {"addressToken": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}}}, "phoneList": {"description": "Phone list will be sorted by the \"dateAdded\" attribute for each phone number. The latest one first.", "type": "array", "items": {"type": "object", "maxItems": 20, "required": ["phoneNumber"], "properties": {"phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the phone number was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedNumber": {"description": "Indicates whether the phone number is the most recently verified successfully for the recipient. A value of true means it is the most recent verified number, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of phone number data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "teleSignVerification": {"description": "Used to determine if the provided phone number is a mobile or landline number.", "type": "object", "required": ["phoneType", "dateTime"], "properties": {"phoneType": {"description": "Telesign determination of phone number category", "type": "string", "enum": ["LANDLINE", "MOBILE", "UNKNOWN"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for telesign verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "addressMatchCode": {"description": "Indicates if the address provided by telesign response matches the address associated with the package or MyChoice enrollment.", "type": "string", "enum": ["MATCHED", "NOT_MATCHED", "NOT_APPLICABLE"]}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the phone", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "emailList": {"description": "Email list will be sorted by the \"dateAdded\" attribute for each email. The latest one first.", "type": "array", "maxItems": 20, "items": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the email was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedEmail": {"description": "Indicates whether the email is the most recently verified successfully for the recipient. A value of true means it is the most recent verified email, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of email data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the email", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "status": {"description": "Status of the recipient record.", "type": "string", "enum": ["active", "inactive"]}}}}}, "required": ["totalResults", "timeStamp"]}, "RecipientData": {"description": "Recipient data entity", "type": "object", "required": ["token", "status"], "properties": {"token": {"description": "UPS consignee token; includes values for My choice address token, CPA (consignee physical address) token, or stopId when no other tokens apply", "type": "string"}, "name": {"type": "string", "example": "<PERSON>"}, "address": {"type": "object", "properties": {"line1": {"description": "Street number line 1", "type": "string", "maxLength": 100, "example": "1000 main st."}, "line2": {"description": "Floor or apartment number", "type": "string", "maxLength": 100, "example": "Apt. 305"}, "line3": {"description": "Cube or suite number", "type": "string", "maxLength": 100, "example": "Suite 24"}, "municipalityName": {"description": "Name of city/municipality.", "type": "string", "maxLength": 100, "example": "<PERSON><PERSON>"}, "stateProvinceCode": {"description": "State/province/county code.", "type": "string", "maxLength": 50, "example": "TX"}, "postalCode": {"description": "Postal code.", "type": "string", "maxLength": 10, "example": "75035"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "addressTypeCode": {"description": "Code to indicate address type, eg. 01 - Residential, 02 - Commercial, 99 - Unspecified/Unclassified", "type": "string", "example": "01"}}}, "trackingContact": {"description": "Tracking contact is only returned when 1) API is called to search by Tracking Number and 2) there's at lease an email or a phone number associated with the tracking number. \nThis data property will contain the email / phone information associated with the specified tracking number.\n", "type": "object", "required": ["trackingNumber"], "minProperties": 2, "properties": {"trackingNumber": {"description": "Schema for tracking number", "type": "string", "pattern": "^[a-zA-Z0-9]{5,36}$", "maxLength": 36}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}}}, "profile": {"description": "UPS.com user profile information.  Only applicable to registered users", "type": "object", "properties": {"userIds": {"description": "List of user id information associated with this recipient", "type": "array", "items": {"type": "object", "required": ["userId", "userRegistrationId", "uuid", "email"], "properties": {"userId": {"description": "UPS.com user id", "type": "string", "minLength": 2, "maxLength": 36, "pattern": "^[a-zA-Z0-9\\-]+$", "example": "shoesLtdTrackUPS"}, "userRegistrationId": {"description": "Registration number for ups.com user", "type": "string", "maxLength": 10, "pattern": "^[a-zA-Z]{2}[0-9]{8}$", "example": "IR00033000"}, "uuid": {"description": "UUID for ups.com user", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9\\-]+$", "example": "4ce27ff5-8e17-4797-a8c1-089796dbed9c"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}, "myChoiceEnrollments": {"description": "List of My Choice for Home enrollments associated with this recipient", "type": "array", "items": {"type": "object", "required": ["addressToken", "email"], "properties": {"addressToken": {"description": "Token representing a name and address, can be any of the following values; My Choice for Home addressToken, CPA token, stopId", "type": "string", "pattern": "^[a-zA-Z0-9]{32,50}$", "maxLength": 50}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}}}}}}, "phoneList": {"description": "Phone list will be sorted by the \"dateAdded\" attribute for each phone number. The latest one first.", "type": "array", "items": {"type": "object", "maxItems": 20, "required": ["phoneNumber"], "properties": {"phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "+15555555555"}, "phoneValidInd": {"description": "boolean indicator if the phone value is valid or invalid.  Even though a phone number meets format requirements, it is not valid for OTP purposes if it is known that it can not belong to an individual, such as toll free numbers and bogus values, such as all 9s, 1234567890, etc.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the phone number was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedNumber": {"description": "Indicates whether the phone number is the most recently verified successfully for the recipient. A value of true means it is the most recent verified number, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of phone number data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "teleSignVerification": {"description": "Used to determine if the provided phone number is a mobile or landline number.", "type": "object", "required": ["phoneType", "dateTime"], "properties": {"phoneType": {"description": "Telesign determination of phone number category", "type": "string", "enum": ["LANDLINE", "MOBILE", "UNKNOWN"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for telesign verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "addressMatchCode": {"description": "Indicates if the address provided by telesign response matches the address associated with the package or MyChoice enrollment.", "type": "string", "enum": ["MATCHED", "NOT_MATCHED", "NOT_APPLICABLE"]}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the phone", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "emailList": {"description": "Email list will be sorted by the \"dateAdded\" attribute for each email. The latest one first.", "type": "array", "maxItems": 20, "items": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "emailValidInd": {"description": "boolean indicator if the email value is valid or invalid.  Even though an email meets format requirements, it is not valid for OTP purposes if it is know that it can not belong to an individual, such as corporate emails, those part of ups.com domain.", "type": "boolean"}, "dateAdded": {"description": "The date and time when the email was added to the recipient's record. The value follows the ISO 8601 date-time format", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}, "isLatestVerifiedEmail": {"description": "Indicates whether the email is the most recently verified successfully for the recipient. A value of true means it is the most recent verified email, while false means it is not.", "type": "boolean", "example": false}, "dataSource": {"description": "Source system of email data.  Initial value will be OPLD (package), and then as updates occur will reflect the system that last updated the data.", "type": "string", "example": "OPLD"}, "lexisNexisVerification": {"description": "LexisNexis verification information", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL", "REVIEW"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Type of reason for LexisNexis verification, such as shipping number.", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Verification reason", "type": "string", "maxLength": 20, "example": "1Z53425345534"}, "isStale": {"description": "Indicate if the verification should be considered stale. If it has been more than one year since the last verification date, it's considered stale.", "type": "boolean"}}}, "otpVerification": {"description": "Indicates if the user has completed the OTP process with UPS and the status of the verification (pass or fail).", "type": "object", "required": ["status", "dateTime"], "properties": {"status": {"description": "Verification status", "type": "string", "enum": ["PASS", "FAIL"]}, "dateTime": {"description": "Verification date time in UTC time", "type": "string", "format": "date-time", "example": "2024-04-05T17:31:00Z"}, "reasonType": {"description": "Specifies type of reason for OTP verification, such as actions by shipping number which trigger the OTP workflow", "type": "string", "enum": ["Shipping Number", "MyChoice for Home"]}, "reason": {"description": "Provides a reference value or details related to the reasonType", "type": "string", "maxLength": 20, "example": "1Z53425345534"}}}, "lastPackageEncounteredDate": {"description": "The date of the last package encountered with the email", "type": "string", "format": "date-time", "example": "2024-04-05T20:31:00Z"}}}}, "status": {"description": "Status of the recipient record.", "type": "string", "enum": ["active", "inactive"]}}}, "CountryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z0-9]{2}$", "example": "US"}, "SuccessResponseHeaders": {"description": "The headers that are returned for all error responses.", "type": "object", "required": ["bkndtransid", "transId"], "properties": {"bkndtransid": {"description": "The backend transaction id, for gateway purpose. bkntransid will have same value as transId if transId is received from client, otherwise random ID will be generated.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}}}, "BackendErrorResponseHeaders": {"description": "The headers that are returned from the backend and gateway", "required": ["bkndtransid", "apierrorcode", "apie<PERSON><PERSON>g"], "properties": {"bkndtransid": {"description": "The backend transaction id.", "type": "string"}, "transId": {"description": "The transaction id received from client", "type": "string"}, "apierrorcode": {"description": "The API error code.", "type": "string", "example": "UJ0001"}, "apierrormsg": {"description": "The API error message.", "type": "string", "example": "Invalid token or token is not present."}}}, "ErrorResponse": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "Error": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}, "Warning": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "RN_CUST_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched customers exceeds 50"}}}}}}