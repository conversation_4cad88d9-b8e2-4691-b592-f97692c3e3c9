{"openapi": "3.0.3", "info": {"title": "Customer 360 Caller Classification API v1", "description": "The Caller Classification API is used by IVR and chatbot systems to determine the type of caller based on their phone number or account number.\nThis API helps route callers to the appropriate workflow by identifying whether they are:\n  - **Preferred Shippers**\n  - **MyChoice users**\n  - **UPS.com ID holders**\n  - **Other customer classifications**\n\n  It does **not** provide full customer or account details, but only returns classification data relevant to call routing.\n\n### Key Features:\n- **Search by Phone & Country**: Identify the caller type based on phone number.\n- **Search by Account & Country**: Retrieve caller classification by UPS account number.\n- **Call Routing Optimization**: Helps IVR & chatbot systems intelligently direct users.\n", "version": "1.0"}, "tags": [{"name": "Caller Classification", "description": "Caller Classification related operations"}], "servers": [{"url": "https://onlinetools.ups.com", "description": "Production"}, {"url": "https://onlinetoolsuat.ups.com", "description": "UAT"}, {"url": "https://onlinetoolspt.ups.com", "description": "PT"}], "security": [{"oauth2": []}], "paths": {"/caller-classification": {"post": {"operationId": "searchCallerCategory", "tags": ["Caller Classification"], "summary": "Determine caller type for IVR & chatbot call routing.", "description": "This endpoint classifies a caller based on: - Phone number & country - Account number & country    The classification helps IVR & chatbot systems determine the best workflow for handling the call. ### Use Cases: - Routing **Preferred Shippers** to dedicated support teams. - Directing **MyChoice users** to self-service options. - Identifying **general UPS customers** for standard workflows.\n", "security": [{"oauth2": []}], "parameters": [{"name": "transId", "in": "header", "description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-]{3,36}$", "minLength": 3, "maxLength": 36, "example": "XZ345445668"}}, {"name": "transactionSrc", "in": "header", "description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-_]{3,30}$", "minLength": 3, "maxLength": 30, "example": "IVR"}}], "x-slo": {"response_time": [{"percentile": 99, "value": "{response time value in milliseconds}"}, {"percentile": 95, "value": "{response time value in milliseconds}"}]}, "requestBody": {"content": {"application/json": {"schema": {"description": "Search criteria options", "type": "object", "required": ["searchCriteria"], "properties": {"searchCriteria": {"description": "Schema for PhoneAndCountry search mode\nSchema for AccountAndCountry search mode", "type": "object", "properties": {"searchType": {"type": "string", "enum": ["PhoneAndCountry"]}, "phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "filterConditions": {"description": "Filter conditions for response. \nsingleAccountByPriority - will return the account with highest priority of contactCategoryCode. \nall - will return all the accounts matched the search criteria. \nIf filterConditions is not provided, default value is all.\n", "type": "array", "items": {"type": "string", "enum": ["singleAccountByPriority", "all"]}}, "accountNumber": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}}}}}}}}, "responses": {"200": {"description": "Successful retrieval of caller category information based on a search mode", "headers": {"bkndtransid": {"description": "The backend transaction id, for gateway purpose.", "schema": {"type": "string", "maxLength": 36}}, "transId": {"description": "The transaction id received from client", "schema": {"type": "string", "maxLength": 36}}}, "content": {"application/json": {"schema": {"description": "Base Response object", "type": "object", "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "WRN_ITEM_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched items exceeds 20"}}}}, "contacts": {"type": "array", "maxItems": 20, "items": {"description": "Response entity for the caller profile.\nCaller category information associated with phone/country/account.", "type": "object", "properties": {"countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "accountNumber": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}, "contactCategoryCode": {"description": "Contact category code.\n| Code  | Priority | Description \n| :---: | :---     | :--- \n| 006   | 1        | Preferred Shipper\n| 001   | 2        | Shipper\n| 004   | 3        | My Choice Business\n| 003   | 4        | My Choice Premium  \n| 002   | 5        | My Choice for Home   \n| 007   | 6        | My Choice Lite   \n| 005   | 7        | UPS.com ID\n| 009   | 8        | Inactive Shipper   \n| 010   | 9        | Non-Account CEC Profile    \n| 008   | 10       | Unknown         \n", "type": "string", "enum": ["001", "002", "003", "004", "005", "006", "007", "008", "009", "010"]}}, "required": ["countryCode", "contactCategoryCode"]}}}, "required": ["timeStamp", "totalResults"]}}}}, "400": {"description": "Validation/Request Error", "headers": {"bkndtransid": {"description": "The backend transaction id, for gateway purpose.", "schema": {"type": "string", "maxLength": 36}}, "transId": {"description": "The transaction id received from client", "schema": {"type": "string", "maxLength": 36}}, "apierrorcode": {"description": "The API error code.", "schema": {"type": "string", "example": "UJ0001"}}, "apierrormsg": {"description": "The API error message.", "schema": {"type": "string", "example": "Invalid token or token is not present."}}}, "content": {"application/json": {"schema": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "example": {"response": {"errors": [{"code": "ERR_VAL_001", "message": "Missing required field {field name}"}]}}}}}, "401": {"description": "Unauthorized", "headers": {"bkndtransid": {"description": "The backend transaction id, for gateway purpose.", "schema": {"type": "string", "maxLength": 36}}, "transId": {"description": "The transaction id received from client", "schema": {"type": "string", "maxLength": 36}}, "apierrorcode": {"description": "The API error code.", "schema": {"type": "string", "example": "UJ0001"}}, "apierrormsg": {"description": "The API error message.", "schema": {"type": "string", "example": "Invalid token or token is not present."}}}, "content": {"application/json": {"schema": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "example": {"response": {"errors": [{"code": "250002", "message": "Invalid OAuth2 Token"}]}}}}}}}}}, "components": {"securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"clientCredentials": {"x-tokenEndpointAuthMethod": "client_secret_basic", "tokenUrl": "/security/v1/oauth/token", "scopes": {}}}}, "JWTAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "parameters": {"transId": {"name": "transId", "in": "header", "description": "An identifier unique to the request.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-]{3,36}$", "minLength": 3, "maxLength": 36, "example": "XZ345445668"}}, "transactionSrc": {"name": "transactionSrc", "in": "header", "description": "Identifies the client/source application that is calling.", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9-_]{3,30}$", "minLength": 3, "maxLength": 30, "example": "IVR"}}}, "headers": {"BkndTransId": {"description": "The backend transaction id, for gateway purpose.", "schema": {"type": "string", "maxLength": 36}}, "TransId": {"description": "The transaction id received from client", "schema": {"type": "string", "maxLength": 36}}, "ApiErrorCode": {"description": "The API error code.", "schema": {"type": "string", "example": "UJ0001"}}, "ApiErrorMsg": {"description": "The API error message.", "schema": {"type": "string", "example": "Invalid token or token is not present."}}}, "schemas": {"Account": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}, "Phone": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "CountryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "PhoneAndCountryCriteria": {"description": "Schema for PhoneAndCountry search mode", "type": "object", "required": ["searchType", "phoneNumber", "countryCode"], "properties": {"searchType": {"type": "string", "enum": ["PhoneAndCountry"]}, "phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "filterConditions": {"description": "Filter conditions for response. \nsingleAccountByPriority - will return the account with highest priority of contactCategoryCode. \nall - will return all the accounts matched the search criteria. \nIf filterConditions is not provided, default value is all.\n", "type": "array", "items": {"type": "string", "enum": ["singleAccountByPriority", "all"]}}}}, "AccountAndCountryCriteria": {"description": "Schema for AccountAndCountry search mode", "type": "object", "required": ["searchType", "accountNumber", "countryCode"], "properties": {"searchType": {"type": "string", "enum": ["AccountAndCountry"]}, "accountNumber": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "filterConditions": {"description": "Filter conditions for response. \nsingleAccountByPriority - will return the account with highest priority of contactCategoryCode. \nall - will return all the accounts matched the search criteria. \nIf filterConditions is not provided, default value is all.\n", "type": "array", "items": {"type": "string", "enum": ["singleAccountByPriority", "all"]}}}}, "SearchRequest": {"description": "Search criteria options", "type": "object", "required": ["searchCriteria"], "properties": {"searchCriteria": {"description": "Schema for PhoneAndCountry search mode\nSchema for AccountAndCountry search mode", "type": "object", "properties": {"searchType": {"type": "string", "enum": ["PhoneAndCountry"]}, "phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "filterConditions": {"description": "Filter conditions for response. \nsingleAccountByPriority - will return the account with highest priority of contactCategoryCode. \nall - will return all the accounts matched the search criteria. \nIf filterConditions is not provided, default value is all.\n", "type": "array", "items": {"type": "string", "enum": ["singleAccountByPriority", "all"]}}, "accountNumber": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}}}}}, "FilterConditions": {"description": "Filter conditions for response. \nsingleAccountByPriority - will return the account with highest priority of contactCategoryCode. \nall - will return all the accounts matched the search criteria. \nIf filterConditions is not provided, default value is all.\n", "type": "array", "items": {"type": "string", "enum": ["singleAccountByPriority", "all"]}}, "BaseResponse": {"description": "Base Response object", "type": "object", "required": ["totalResults", "timeStamp"], "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "WRN_ITEM_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched items exceeds 20"}}}}}}, "CallerClassificationResponse": {"description": "Base Response object", "type": "object", "properties": {"totalResults": {"description": "Number of results that match the search criteria.", "type": "integer"}, "timeStamp": {"description": "Response timestamp", "type": "string", "format": "date-time"}, "warning": {"description": "warning", "type": "array", "items": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "WRN_ITEM_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched items exceeds 20"}}}}, "contacts": {"type": "array", "maxItems": 20, "items": {"description": "Response entity for the caller profile.\nCaller category information associated with phone/country/account.", "type": "object", "properties": {"countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "accountNumber": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}, "contactCategoryCode": {"description": "Contact category code.\n| Code  | Priority | Description \n| :---: | :---     | :--- \n| 006   | 1        | Preferred Shipper\n| 001   | 2        | Shipper\n| 004   | 3        | My Choice Business\n| 003   | 4        | My Choice Premium  \n| 002   | 5        | My Choice for Home   \n| 007   | 6        | My Choice Lite   \n| 005   | 7        | UPS.com ID\n| 009   | 8        | Inactive Shipper   \n| 010   | 9        | Non-Account CEC Profile    \n| 008   | 10       | Unknown         \n", "type": "string", "enum": ["001", "002", "003", "004", "005", "006", "007", "008", "009", "010"]}}, "required": ["countryCode", "contactCategoryCode"]}}}, "required": ["timeStamp", "totalResults"]}, "CallerData": {"description": "Response entity for the caller profile.\nCaller category information associated with phone/country/account.", "type": "object", "properties": {"countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "accountNumber": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}, "contactCategoryCode": {"description": "Contact category code.\n| Code  | Priority | Description \n| :---: | :---     | :--- \n| 006   | 1        | Preferred Shipper\n| 001   | 2        | Shipper\n| 004   | 3        | My Choice Business\n| 003   | 4        | My Choice Premium  \n| 002   | 5        | My Choice for Home   \n| 007   | 6        | My Choice Lite   \n| 005   | 7        | UPS.com ID\n| 009   | 8        | Inactive Shipper   \n| 010   | 9        | Non-Account CEC Profile    \n| 008   | 10       | Unknown         \n", "type": "string", "enum": ["001", "002", "003", "004", "005", "006", "007", "008", "009", "010"]}}, "required": ["countryCode", "contactCategoryCode"]}, "CallerDetail": {"description": "Response entity for the caller profile.", "type": "object", "minItems": 2, "required": ["countryCode"], "properties": {"countryCode": {"description": "ISO 3166 alpha-2 country code.", "type": "string", "maxLength": 2, "pattern": "^[a-zA-Z]{2}$", "example": "US"}, "phoneNumber": {"type": "string", "pattern": "^\\+?\\d{1,15}$", "example": "***********"}, "accountNumber": {"description": "UPS Shipping Account Number", "type": "string", "pattern": "^[a-zA-Z0-9]{6,10}$", "minLength": 6, "maxLength": 10, "example": "AA111111"}}}, "CallerClassification": {"description": "Caller category information associated with phone/country/account.", "type": "object", "required": ["contactCategoryCode"], "properties": {"contactCategoryCode": {"description": "Contact category code.\n| Code  | Priority | Description \n| :---: | :---     | :--- \n| 006   | 1        | Preferred Shipper\n| 001   | 2        | Shipper\n| 004   | 3        | My Choice Business\n| 003   | 4        | My Choice Premium  \n| 002   | 5        | My Choice for Home   \n| 007   | 6        | My Choice Lite   \n| 005   | 7        | UPS.com ID\n| 009   | 8        | Inactive Shipper   \n| 010   | 9        | Non-Account CEC Profile    \n| 008   | 10       | Unknown         \n", "type": "string", "enum": ["001", "002", "003", "004", "005", "006", "007", "008", "009", "010"]}}}, "ErrorResponse": {"type": "object", "description": "response container", "required": ["response"], "properties": {"response": {"type": "object", "description": "errors container", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "error payload - array containing one or more Errors", "items": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}}}}}}, "Error": {"type": "object", "description": "The error entity that contains the code and description of an error.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The error code."}, "message": {"type": "string", "description": "The error message."}}}, "Warning": {"type": "object", "description": "The warning entity that contains the code and description of an warning.", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code for warning.", "example": "WRN_ITEM_LIMIT_EXCEED"}, "message": {"type": "string", "description": "The warning message.", "example": "The response only contains partial data. Matched items exceeds 20"}}}}}}