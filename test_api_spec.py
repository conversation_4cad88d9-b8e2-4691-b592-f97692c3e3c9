#!/usr/bin/env python3
"""
Test script to validate the downgraded API specification.
This script performs comprehensive validation to ensure the API spec
is compatible with Watson Assistant and other OpenAPI tools.
"""

import json
import sys
from openapi_spec_validator import validate_spec
from openapi_spec_validator.exceptions import OpenAPISpecValidatorError


def test_json_validity(file_path):
    """Test if the JSON file is valid."""
    print(f"Testing JSON validity of {file_path}...")
    try:
        with open(file_path, 'r') as f:
            spec = json.load(f)
        print("✅ JSON is valid")
        return spec
    except json.JSONDecodeError as e:
        print(f"❌ JSON validation failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return None


def test_openapi_validity(spec):
    """Test if the OpenAPI specification is valid."""
    print("Testing OpenAPI specification validity...")
    try:
        validate_spec(spec)
        print("✅ OpenAPI specification is valid")
        return True
    except OpenAPISpecValidatorError as e:
        print(f"❌ OpenAPI validation failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        return False


def test_specific_fixes(spec):
    """Test specific fixes that were applied."""
    print("Testing specific fixes...")
    
    # Test CtiVoiceRequestV1 schema
    schemas = spec.get('components', {}).get('schemas', {})
    if 'CtiVoiceRequestV1' not in schemas:
        print("❌ CtiVoiceRequestV1 schema not found")
        return False
    
    schema = schemas['CtiVoiceRequestV1']
    properties = schema.get('properties', {})
    
    if 'trackingnumber' not in properties:
        print("❌ trackingnumber property not found in CtiVoiceRequestV1")
        return False
    
    trackingnumber = properties['trackingnumber']
    
    # Check array type
    if trackingnumber.get('type') != 'array':
        print(f"❌ trackingnumber type is {trackingnumber.get('type')}, expected 'array'")
        return False
    
    # Check items maxLength
    items = trackingnumber.get('items', {})
    max_length = items.get('maxLength')
    
    if not isinstance(max_length, int):
        print(f"❌ trackingnumber items maxLength is {type(max_length)}, expected int")
        return False
    
    if max_length != 30:
        print(f"❌ trackingnumber items maxLength is {max_length}, expected 30")
        return False
    
    print("✅ All specific fixes are correct")
    return True


def test_no_invalid_syntax(spec):
    """Test that there are no invalid JSON syntax issues."""
    print("Testing for invalid JSON syntax...")
    
    def check_object(obj, path=""):
        """Recursively check for invalid syntax."""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                # Check for invalid maxLength values
                if key == 'maxLength':
                    if isinstance(value, str):
                        print(f"❌ Found string maxLength at {current_path}: {value}")
                        return False
                    elif not isinstance(value, (int, float)):
                        print(f"❌ Found invalid maxLength type at {current_path}: {type(value)}")
                        return False
                
                # Recursively check nested objects
                if not check_object(value, current_path):
                    return False
                    
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                if not check_object(item, f"{path}[{i}]"):
                    return False
        
        return True
    
    if check_object(spec):
        print("✅ No invalid JSON syntax found")
        return True
    else:
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("API Specification Validation Test")
    print("=" * 60)
    
    file_path = 'fixed_APISpecs/api_spec.json'
    
    # Test 1: JSON validity
    spec = test_json_validity(file_path)
    if spec is None:
        sys.exit(1)
    
    # Test 2: OpenAPI validity
    if not test_openapi_validity(spec):
        sys.exit(1)
    
    # Test 3: Specific fixes
    if not test_specific_fixes(spec):
        sys.exit(1)
    
    # Test 4: No invalid syntax
    if not test_no_invalid_syntax(spec):
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED!")
    print("The API specification is ready for Watson Assistant import.")
    print("=" * 60)


if __name__ == "__main__":
    main()
