import copy
from utils.crawl_open_api_spec import crawl_open_api_spec
from utils.read_open_api_path import read_open_api_path
from utils.replace_at_path import replace_at_path

def allOf(components):

    description = "\n".join([component["description"] for component in components if "description" in component])

    required = []
    for component in components:
        required.extend(component.get("required", []))
    required = list(set(required))
    
    properties = {}
    for component in components:
        for key, value in component.get("properties", {}).items():
            if key in properties:
                continue
                #raise ValueError(f"Cannot merge due to duplicate property name - {key}")
            properties[key] = value

    merged_component = {
        "description": description,
        "type": "object",
        "properties": properties
    }
    if required:
        merged_component["required"] = required
    return merged_component


def anyOf(components):

    description = "\n".join([component["description"] for component in components if "description" in component])

    required = []
    
    properties = {}
    for component in components:
        for key, value in component.get("properties", {}).items():
            if key in properties:
                continue
                #raise ValueError(f"Cannot merge due to duplicate property name - {key}")
            properties[key] = value

    merged_component = {
        "description": description,
        "type": "object",
        "properties": properties
    }
    if required:
        merged_component["required"] = required
    return merged_component

def oneOf(components):
    return anyOf(components)

def get_list_of_refs(open_api_spec, path, any_one_all_of_field):
    obj_with_refs = read_open_api_path(open_api_spec, path)
    list_of_refs = [obj["$ref"] for obj in obj_with_refs[any_one_all_of_field]]
    return list_of_refs

def merge(open_api_spec, path_to_any_all_one_of):
    try:
        any_all_one_of_obj = read_open_api_path(open_api_spec, path_to_any_all_one_of)
    except:
        # Path might not exist anymore due to previous merges
        return open_api_spec

    if not isinstance(any_all_one_of_obj, dict):
        # Object is not a dict, skip
        return open_api_spec

    any_all_one_of_field = None
    for key in any_all_one_of_obj:
        if key in ["anyOf", "allOf", "oneOf"]:
            any_all_one_of_field = key
            break

    if any_all_one_of_field is None:
        # No anyOf/allOf/oneOf field found, skip
        return open_api_spec

    components = []
    for obj in any_all_one_of_obj[any_all_one_of_field]:
        if "$ref" in obj:
            components.append( read_open_api_path(open_api_spec, obj["$ref"]) )
        else:
            components.append(obj)

    merged_obj = None
    if any_all_one_of_field == "allOf":
        merged_obj = allOf(components)
    if any_all_one_of_field == "anyOf":
        merged_obj = anyOf(components)
    if any_all_one_of_field == "oneOf":
        merged_obj = oneOf(components)

    if merged_obj is None:
        raise Exception(f"Something didn't work\n{any_all_one_of_field}")

    return replace_at_path(open_api_spec, path_to_any_all_one_of, merged_obj)

def get_any_all_one_of_paths(open_api_spec):
    all_paths = crawl_open_api_spec(open_api_spec)
    any_of_paths = set([])
    one_of_paths = set([])
    all_of_paths = set([])
    for path in all_paths:
        *path_dir, key = path.split("/")
        if key == "anyOf":
            any_of_paths.add("/".join(path_dir))
        elif key == "oneOf":
            one_of_paths.add("/".join(path_dir))
        elif key == "allOf":
            all_of_paths.add("/".join(path_dir))

    # The order does matter due to nesting. This order is a pretty good heuristic. Usually "allOf" should be done last
    paths_to_any_all_one_of = list(any_of_paths) + list(one_of_paths) + list(all_of_paths)
    return paths_to_any_all_one_of

def remove_examples_field(open_api_spec):
    all_paths = crawl_open_api_spec(open_api_spec)
    for path in all_paths:
        *_, key = path.split("/")
        if key == "examples":
            replace_at_path(open_api_spec, path, None)

def resolve_composites_recursion(open_api_spec, paths_to_any_all_one_of, verbose):
    for path in paths_to_any_all_one_of:
        if verbose:
            print(path)
        open_api_spec = merge(open_api_spec, path)
    return open_api_spec

def resolve_composites(open_api_spec, paths_to_any_all_one_of=None, verbose=False):
    user_defined_paths = (paths_to_any_all_one_of is not None)
    open_api_spec = copy.deepcopy(open_api_spec)

    open_api_spec["openapi"] = "3.0.3"

    if paths_to_any_all_one_of is None:
        paths_to_any_all_one_of = get_any_all_one_of_paths(open_api_spec)
    
    open_api_spec = resolve_composites_recursion(open_api_spec, paths_to_any_all_one_of, verbose)
    while (not user_defined_paths) and (paths_to_any_all_one_of != []):
        paths_to_any_all_one_of = get_any_all_one_of_paths(open_api_spec)
        open_api_spec = resolve_composites_recursion(open_api_spec, paths_to_any_all_one_of, verbose)

    # OpenAPI 3.0 does not support "examples". It needs to be "exmample". So for now we'll just remove this field
    remove_examples_field(open_api_spec)
    return open_api_spec